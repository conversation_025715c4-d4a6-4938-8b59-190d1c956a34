<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SysNoticeOssMapper">

    <select id="selectOssIdsByNoticeId" resultType="Long">
        select u.oss_id from sys_oss u
        inner join sys_notice_oss sur
            on u.oss_id = sur.oss_id and sur.notice_id = #{noticeId}
    </select>

    <select id="selectNoticeOssByNoticeId" resultType="org.dromara.system.domain.SysNoticeOss">
        select sur.* from sys_notice_oss sur
        where sur.notice_id = #{noticeId}
    </select>

    <update id="increaseDownloadCount">
        update sys_notice_oss set download_count = download_count + 1 where notice_id = #{noticeId} and oss_id = #{ossId}
    </update>

    <select id="getDownloadCount" resultType="int">
        select COALESCE(download_count, 0) as download_count
        from sys_notice_oss
        where notice_id = #{noticeId} and oss_id = #{ossId}
    </select>

</mapper>
