package org.dromara.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.bo.SysLogArchiveConfigBo;
import org.dromara.system.domain.vo.SysLogArchiveConfigVo;
import org.dromara.system.service.ISysLogArchiveConfigService;
import org.dromara.system.service.ISysLogArchiveService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 日志转存管理控制器
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/logArchive")
public class SysLogArchiveController extends BaseController {

    private final ISysLogArchiveConfigService configService;
    private final ISysLogArchiveService archiveService;

    /**
     * 查询日志转存配置列表
     */
    @SaCheckPermission("system:logArchive:list")
    @GetMapping("/config/list")
    public TableDataInfo<SysLogArchiveConfigVo> configList(SysLogArchiveConfigBo bo, PageQuery pageQuery) {
        return configService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取当前配置
     */
    @SaCheckPermission("system:logArchive:query")
    @GetMapping("/config/current")
    public R<SysLogArchiveConfigVo> getCurrentConfig() {
        return R.ok(configService.getCurrentConfig());
    }

    /**
     * 新增日志转存配置
     */
    @SaCheckPermission("system:logArchive:add")
    @Log(title = "日志转存配置", businessType = BusinessType.INSERT)
    @PostMapping("/config")
    public R<Void> addConfig(@Validated @RequestBody SysLogArchiveConfigBo bo) {
        return toAjax(configService.insertByBo(bo));
    }

    /**
     * 修改日志转存配置
     */
    @SaCheckPermission("system:logArchive:edit")
    @Log(title = "日志转存配置", businessType = BusinessType.UPDATE)
    @PutMapping("/config")
    public R<Void> editConfig(@Validated @RequestBody SysLogArchiveConfigBo bo) {
        return toAjax(configService.updateByBo(bo));
    }

    /**
     * 手动执行日志转存
     */
    @SaCheckPermission("system:logArchive:execute")
    @Log(title = "手动日志转存", businessType = BusinessType.OTHER)
    @PostMapping("/execute")
    public R<Void> executeArchive() {
        try {
            archiveService.manualArchiveLogs();
            return R.ok("日志转存执行成功");
        } catch (Exception e) {
            return R.fail("日志转存执行失败：" + e.getMessage());
        }
    }

    /**
     * 检查告警条件
     */
    @SaCheckPermission("system:logArchive:alarm")
    @PostMapping("/alarm/check")
    public R<Void> checkAlarm() {
        try {
//            archiveService.checkAlarmConditions();
            String a = "1";
            return R.ok("告警检查完成");
        } catch (Exception e) {
            return R.fail("告警检查失败：" + e.getMessage());
        }
    }
}
