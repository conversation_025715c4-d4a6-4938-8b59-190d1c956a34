package org.dromara.cssrcFinance.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 财务-预算-支出计划对象 finance_expenditure_plan
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("finance_expenditure_plan")
public class FinanceExpenditurePlan extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 支出类别（材料费、专用费、外协费、分拨款）
     */
    private String expenditureType;

    /**
     * 内容
     */
    private String content;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 课题编号
     */
    private String subjectNumber;

    /**
     * 课题名称
     */
    private String subjectName;

    /**
     * 项目类别
     */
    private String projectType;

    /**
     * 合同总金额（万元）
     */
    private Long contractTotalAmount;

    /**
     * 本次付款金额（万元）
     */
    private Long currentPaymentAmount;

    /**
     * 付款时间
     */
    private Date paymentTime;

    /**
     * 是否零余额项目（Y表示是，N表示否）
     */
    private String isNobalanceProject;

    /**
     * 是否重点项目（Y表示是，N表示否）
     */
    private String isMajorProject;

    /**
     * 项目是否到款（Y表示是，N表示否）
     */
    private String isFunded;

    /**
     * 支付方式（银行转账、银行承兑）
     */
    private String paymentMethod;

    /**
     * 流程状态
     */
    private String flowStatus;


}
