package org.dromara.cssrcSoftware.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import org.dromara.common.encrypt.annotation.EncryptField;
import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 软件基本信息对象 software_info
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("software_info")
@Schema(description = "软件信息")
public class SoftwareInfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 软件ID
     */
    @TableId(value = "software_id")
    private Long softwareId;

    /**
     * 软件分类ID
     */
    private Long categoryId;

    /**
     * 软件名称
     */
    private String softwareName;

    /**
     * 生产厂商
     */
    private String manufacturer;

    /**
     * 生产国别
     */
    private String country;

    /**
     * 软件简介
     */
    private String intro;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 下载次数
     */
    private Long downloadCount;

    /**
     * 软件密级
     */
    @EncryptField
    private String secret;
}
