package org.dromara.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.service.DictService;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.SysNotice;
import org.dromara.system.domain.bo.SysNoticeBo;
import org.dromara.system.domain.vo.SysNoticeVo;
import org.dromara.system.service.ISysNoticeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公告 信息操作处理
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/notice")
public class SysNoticeController extends BaseController {

    private final ISysNoticeService noticeService;
    private final DictService dictService;

    /**
     * 获取通知公告列表（用户首页）
     */
    @SaCheckPermission("system:notice:list")
    @GetMapping("/list")
    public TableDataInfo<SysNoticeVo> list(SysNoticeBo notice, PageQuery pageQuery) {
        return noticeService.selectPageNoticeList(notice, pageQuery);
    }

    /**
     * 获取通知公告列表（后台管理页面）
     */
    @SaCheckPermission("system:notice:list")
    @GetMapping("/managementList")
    public TableDataInfo<SysNoticeVo> managementList(SysNoticeBo notice, PageQuery pageQuery) {
        return noticeService.selectPageNoticeManagementList(notice, pageQuery);
    }

    /**
     * 根据通知公告编号获取详细信息
     *
     * @param noticeId 公告ID
     */
    @SaCheckPermission("system:notice:query")
    @GetMapping(value = "/{noticeId}")
    public R<SysNoticeVo> getInfo(@PathVariable Long noticeId) {
        return R.ok(noticeService.selectNoticeById(noticeId));
    }

    /**
     * 新增通知公告
     */
    @SaCheckPermission("system:notice:add")
    @Log(
        title = "通知公告",
        businessType = BusinessType.INSERT,
        requestObjSpel = "#notice",
        tableEntity = SysNotice.class
    )
    @PostMapping
    public R<SysNoticeVo> add(@Validated @RequestBody SysNoticeBo notice) {

//        // 发送通知公告消息
//        String type = dictService.getDictLabel("sys_notice_type", notice.getNoticeType());
//        SseMessageUtils.publishAll("[" + type + "] " + notice.getNoticeTitle());

        return R.ok(noticeService.insertNotice(notice));
    }

    /**
     * 修改通知公告
     */
    @SaCheckPermission("system:notice:edit")
    @Log(
        title = "通知公告",
        businessType = BusinessType.UPDATE,
        requestObjSpel = "#notice",
        operatorObjIdSpel = "#notice.getNoticeId()",
        tableEntity = SysNotice.class
    )
    @PutMapping
    public R<SysNoticeVo> edit(@Validated @RequestBody SysNoticeBo notice) {
        return R.ok(noticeService.updateNotice(notice));
    }

    /**
     * 删除通知公告
     *
     * @param noticeIds 公告ID串
     */
    @SaCheckPermission("system:notice:remove")
    @Log(
        title = "通知公告",
        businessType = BusinessType.DELETE,
        operatorObjIdSpel = "#noticeIds",
        tableEntity = SysNotice.class
    )
    @DeleteMapping("/{noticeIds}")
    public R<Void> remove(@PathVariable List<Long> noticeIds) {
        return toAjax(noticeService.deleteNoticeByIds(noticeIds));
    }

    /**
     * 增加附件下载次数计数
     */
    @PutMapping("/increaseNoticeOssDownloadCount")
    public R<Integer> increaseNoticeOssDownloadCount(@RequestParam  Long noticeId, @RequestParam  Long ossId) {
        int count = noticeService.increaseNoticeOssDownloadCount(noticeId, ossId);
        if (count <= 0) {
            return R.fail("更新下载次数失败");
        }
        return R.ok(count);
    }

    /**
     * 删除通知公告和文件的关联关系
     * @param noticeId
     * @param ossIds
     * @return
     */
    @PutMapping("/deleteNoticeOssRelation")
    public R<Boolean> deleteNoticeOssRelation(@RequestParam Long noticeId, @RequestParam List<Long> ossIds) {
        if (noticeService.deleteNoticeOssRelation(noticeId, ossIds)) {
            return R.ok(true);
        } else {
            return R.fail("删除通知公告，文件关联记录失败");
        }
    }
}
