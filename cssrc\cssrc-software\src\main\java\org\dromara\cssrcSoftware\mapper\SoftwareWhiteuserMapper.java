package org.dromara.cssrcSoftware.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.dromara.cssrcSoftware.domain.SoftwareWhiteuser;
import org.dromara.cssrcSoftware.domain.vo.SoftwareWhiteuserVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 软件权限白名单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@DS("software")
public interface SoftwareWhiteuserMapper extends BaseMapperPlus<SoftwareWhiteuser, SoftwareWhiteuserVo> {

}
