package org.dromara.cssrcSoftware.domain.vo;

import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.cssrcSoftware.domain.SoftwareAccess;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 软件入网申请视图对象 software_access
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SoftwareAccess.class)
public class SoftwareAccessVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 申请id
     */
    @ExcelProperty(value = "申请id")
    private Long accessId;

    /**
     * 入网类型
     */
    @ExcelProperty(value = "入网类型")
    private String accessType;

    /**
     * 入网申请
     */
    @ExcelProperty(value = "入网申请")
    private String accessKind;

    /**
     * 责任人
     */
    @ExcelProperty(value = "责任人")
    private Long accessResponsible;

    /**
     * 创建人工号
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "accessResponsible")
    private String accessResponsibleName;

    /**
     * 责任人
     */
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "accessResponsible")
    private String accessResponsibleNickName;

    /**
     * 计算机联网类型
     */
    @ExcelProperty(value = "计算机联网类型")
    private String networkType;

    /**
     * 计算机密级编号
     */
    @ExcelProperty(value = "计算机密级编号")
    private String computerCode;

    /**
     * 软件分类ID
     */
    private Long categoryId;

    /**
     * 软件ID
     */
    private Long softwareId;

    /**
     * 软件名称
     */
    @ExcelProperty(value = "软件名称")
    private String softwareName;

    /**
     * 软件密级
     */
    @ExcelProperty(value = "软件密级")
    private String secret;

    /**
     * 生产厂商
     */
    @ExcelProperty(value = "生产厂商")
    private String manufacturer;

    /**
     * 生产国别
     */
    @ExcelProperty(value = "生产国别")
    private String country;

    /**
     * 软件简介
     */
    @ExcelProperty(value = "软件简介")
    private String intro;

    /**
     * 软件版本ID
     */
    private Long versionId;

    /**
     * 版本号（如 v1.0.0）
     */
    @ExcelProperty(value = "版本号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如v1.0.0")
    private String versionName;

    /**
     * 平台（windows/linux）
     */
    @ExcelProperty(value = "平台", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "windows/linux")
    private String platform;

    /**
     * 架构（x86/x64/arm64）
     */
    @ExcelProperty(value = "架构", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "x86/x64/arm64")
    private String architecture;

    /**
     * 包类型（exe/msi/deb/rpm/tar.gz）
     */
    @ExcelProperty(value = "包类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "exe/msi/deb/rpm/tar.gz")
    private String packageType;

    /**
     * 位数（32/64）
     */
    @ExcelProperty(value = "位数", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "32/64")
    private String bits;

    /**
     * 文件id
     */
    @ExcelProperty(value = "文件id")
    private Long ossId;

    /**
     * 文件大小(GB)
     */
    @ExcelProperty(value = "文件大小(GB)")
    private Long fileSize;

    /**
     * 版本备注
     */
    @ExcelProperty(value = "版本备注")
    private String remark;

    /**
     * 流程状态
     */
    @ExcelProperty(value = "流程状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wf_business_status")
    private String flowStatus;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建人工号
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;

    /**
     * 创建人名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "createBy")
    private String createByNickName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建部门名称
     */
    @Translation(type = TransConstant.DEPT_ID_TO_NAME, mapper = "createDept")
    private String createDeptName;

    /**
     * 更新时间
     */
    private Date updateTime;
}
