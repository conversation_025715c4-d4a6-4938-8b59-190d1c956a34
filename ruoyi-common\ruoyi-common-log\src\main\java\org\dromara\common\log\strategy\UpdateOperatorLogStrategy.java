package org.dromara.common.log.strategy;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.log.content.ObjectUpdateLogUtil;
import org.dromara.common.log.content.OperatorLogMetaDataBuilder;
import org.dromara.common.log.utils.OperatorLogContext;
import org.dromara.common.log.utils.OperatorLogUtil;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * 更新操作日志生成变更内容模板
 *
 * <AUTHOR>
 */

@Slf4j
@Component
public class UpdateOperatorLogStrategy implements IOperateLogStrategy {
    @Override
    public String getOperatorContent(Object requestObj, Class<?>[] tableEntity, String operatorObjId) throws IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        Object oldData = OperatorLogContext.getOldData();
        Object newData = OperatorLogContext.getNewData();
        ObjectUpdateLogUtil objectUpdateLogUtil = new ObjectUpdateLogUtil();
        OperatorLogMetaDataBuilder builder = new OperatorLogMetaDataBuilder();
        List<String> result = objectUpdateLogUtil.generatorChangeLog(
            builder.getChangeModel(oldData, tableEntity[0]),
            builder.getChangeModel(newData, tableEntity[0]),
            OperatorLogUtil.getTableComment(tableEntity[0]),
            tableEntity[0]
        );
        OperatorLogContext.clear();
        return String.join(";", result);
    }
}

