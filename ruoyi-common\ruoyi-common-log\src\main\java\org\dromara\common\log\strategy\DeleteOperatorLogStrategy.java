package org.dromara.common.log.strategy;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.log.content.ObjectDeleteLogUtil;
import org.dromara.common.log.content.OperatorLogContentUtil;
import org.dromara.common.log.content.OperatorLogMetaData;
import org.dromara.common.log.content.OperatorLogMetaDataBuilder;
import org.dromara.common.log.utils.OperatorLogContext;
import org.dromara.common.log.utils.OperatorLogUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 删除生成变更内容模板
 * <AUTHOR>
 * @Date: 2023/09/25
 * @Description:  删除生成变更内容模板
 */
@Slf4j
@Component
public class DeleteOperatorLogStrategy implements IOperateLogStrategy {
    @Override
    public String getOperatorContent(Object requestObj, Class<?>[] tableEntity, String operatorObjId) {
        try {
            String objectType = OperatorLogUtil.getTableComment(tableEntity[0]);
            Object oldData = OperatorLogContext.getOldData();
            ObjectDeleteLogUtil objectDeleteLogUtil = new ObjectDeleteLogUtil();

            if (oldData instanceof Collection) {
                Collection<?> collection = (Collection<?>) oldData;
                if (collection.size() == 1) {
                    Object obj = collection.iterator().next();
                    return objectDeleteLogUtil.generatorContent(obj, objectType, operatorObjId, tableEntity[0]);
                } else {
                    List<String> objNames = new ArrayList<>();
                    List<String> objIds = new ArrayList<>();

                    for (Object obj : collection) {
                        OperatorLogMetaData metaData = new OperatorLogMetaDataBuilder().getChangeModel(obj, tableEntity[0]);
                        // 优先使用displayName，如果没有则使用name
                        String displayName = metaData.getDisplayName();
                        if (displayName == null) {
                            displayName = metaData.getName();
                        }
                        objNames.add(displayName);
                        objIds.add(metaData.getId());
                    }

                    // 批量删除时只显示对象名称和ID，不显示详细字段信息，避免日志过于冗长
                    String batchContent = String.format("共删除%d条记录", collection.size());
                    return OperatorLogContentUtil.deleteBatchObjFormat(objNames, objectType, objIds, batchContent);
                }
            }

            return objectDeleteLogUtil.generatorContent(oldData, objectType, operatorObjId, tableEntity[0]);
        } catch (Exception e) {
            log.error("生成删除对象操作日志失败", e);
            return "生成删除日志失败:" + e.getMessage();
        } finally {
            OperatorLogContext.clear();
        }
    }



}
