package org.dromara.cssrcSoftware.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.cssrcSoftware.domain.SoftwareDownloadLog;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 软件下载日志视图对象 software_download_log
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SoftwareDownloadLog.class)
public class SoftwareDownloadLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    @ExcelProperty(value = "日志ID")
    private Long logId;

    /**
     * 软件ID
     */
//    @ExcelProperty(value = "软件ID")
    private Long softwareId;

    /**
     * 软件名称
     */
    @ExcelProperty(value = "软件名称")
    private String softwareName;

    /**
     * 版本ID
     */
//    @ExcelProperty(value = "版本ID")
    private Long versionId;

    /**
     * 版本名称
     */
    @ExcelProperty(value = "版本名称")
    private String versionName;

    /**
     * 下载用户ID
     */
//    @ExcelProperty(value = "下载用户ID")
    private Long userId;

    /**
     * 下载用户姓名
     */
    @ExcelProperty(value = "下载用户姓名")
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "userId")
    private String nickName;

    /**
     * 下载IP
     */
    @ExcelProperty(value = "下载IP")
    private String ipAddress;

    /**
     * 下载时间
     */
    @ExcelProperty(value = "下载时间")
    private Date downloadTime;

    /**
     * 下载状态
     */
    @ExcelProperty(value = "下载状态")
    private String status;


}
