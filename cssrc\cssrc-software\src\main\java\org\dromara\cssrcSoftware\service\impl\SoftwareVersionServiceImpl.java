package org.dromara.cssrcSoftware.service.impl;

import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.domain.vo.SysStandardVo;
import org.dromara.system.service.ISysOssService;
import org.springframework.stereotype.Service;
import org.dromara.cssrcSoftware.domain.bo.SoftwareVersionBo;
import org.dromara.cssrcSoftware.domain.vo.SoftwareVersionVo;
import org.dromara.cssrcSoftware.domain.SoftwareVersion;
import org.dromara.cssrcSoftware.mapper.SoftwareVersionMapper;
import org.dromara.cssrcSoftware.service.ISoftwareVersionService;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 软件版本详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RequiredArgsConstructor
@Service
public class SoftwareVersionServiceImpl implements ISoftwareVersionService {

    private final SoftwareVersionMapper baseMapper;
    private final ISysOssService ossService;

    /**
     * 查询软件版本详情
     *
     * @param versionId 主键
     * @return 软件版本详情
     */
    @Override
    public SoftwareVersionVo queryById(Long versionId){
        return baseMapper.selectVoById(versionId);
    }

    /**
     * 分页查询软件版本详情列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 软件版本详情分页列表
     */
    @Override
    public TableDataInfo<SoftwareVersionVo> queryPageList(SoftwareVersionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SoftwareVersion> lqw = buildQueryWrapper(bo);
        Page<SoftwareVersionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 填充文件信息
        List<SoftwareVersionVo> filteredRecords;
        filteredRecords = result.getRecords().stream()
            .collect(Collectors.toList());
        for (SoftwareVersionVo vo : filteredRecords) {
            fillingDataAfterSave(vo);
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的软件版本详情列表
     *
     * @param bo 查询条件
     * @return 软件版本详情列表
     */
    @Override
    public List<SoftwareVersionVo> queryList(SoftwareVersionBo bo) {
        LambdaQueryWrapper<SoftwareVersion> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SoftwareVersion> buildQueryWrapper(SoftwareVersionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SoftwareVersion> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SoftwareVersion::getVersionId);
        lqw.eq(bo.getSoftwareId() != null, SoftwareVersion::getSoftwareId, bo.getSoftwareId());
        lqw.like(StringUtils.isNotBlank(bo.getVersionName()), SoftwareVersion::getVersionName, bo.getVersionName());
        lqw.eq(StringUtils.isNotBlank(bo.getPlatform()), SoftwareVersion::getPlatform, bo.getPlatform());
        lqw.eq(StringUtils.isNotBlank(bo.getArchitecture()), SoftwareVersion::getArchitecture, bo.getArchitecture());
        lqw.eq(StringUtils.isNotBlank(bo.getPackageType()), SoftwareVersion::getPackageType, bo.getPackageType());
        lqw.eq(StringUtils.isNotBlank(bo.getBits()), SoftwareVersion::getBits, bo.getBits());
        lqw.eq(bo.getOssId() != null, SoftwareVersion::getOssId, bo.getOssId());
        lqw.eq(bo.getFileSize() != null, SoftwareVersion::getFileSize, bo.getFileSize());
        lqw.eq(StringUtils.isNotBlank(bo.getApprovalStatus()), SoftwareVersion::getApprovalStatus, bo.getApprovalStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getDisplayStatus()), SoftwareVersion::getDisplayStatus, bo.getDisplayStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SoftwareVersion::getStatus, bo.getStatus());
        lqw.eq(bo.getDownloadCount() != null, SoftwareVersion::getDownloadCount, bo.getDownloadCount());
        return lqw;
    }

    /**
     * 新增软件版本详情
     *
     * @param bo 软件版本详情
     * @return 是否新增成功
     */
    @Override
    public SoftwareVersionVo insertByBo(SoftwareVersionBo bo) {
        SoftwareVersion add = MapstructUtils.convert(bo, SoftwareVersion.class);
        validEntityBeforeSave(add);
        SoftwareVersionVo vo = MapstructUtils.convert(add, SoftwareVersionVo.class);
        return fillingDataAfterSave(vo);
    }

    /**
     * 修改软件版本详情
     *
     * @param bo 软件版本详情
     * @return 是否修改成功
     */
    @Override
    public SoftwareVersionVo updateByBo(SoftwareVersionBo bo) {
        SoftwareVersion update = MapstructUtils.convert(bo, SoftwareVersion.class);
        validEntityBeforeSave(update);
        int flag = baseMapper.updateById(update);
        if (flag < 1) {
            throw new ServiceException("修改版本" + update.getVersionId() + "失败");
        }

        SoftwareVersionVo vo = MapstructUtils.convert(update, SoftwareVersionVo.class);
        return fillingDataAfterSave(vo);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SoftwareVersion entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 返回 Vo 给前端前填充必要信息，例如根据存储的oss_id填充文件信息
     * 因为仅存储 oss_id 不够前端使用
     * @param vo
     * @return
     */
    private SoftwareVersionVo fillingDataAfterSave(SoftwareVersionVo vo) {
        if (vo.getOssId() != null) {
            SysOssVo oss = ossService.getById(vo.getOssId());
            if (oss != null) {
                // 填充文件数据
                vo.setVersionFileName(oss.getOriginalName());
                vo.setVersionFileSecret(oss.getFileSecret());
                vo.setVersionFileUrl(oss.getUrl());
            }
        }
        return vo;
    }

    /**
     * 校验并批量删除软件版本详情信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
