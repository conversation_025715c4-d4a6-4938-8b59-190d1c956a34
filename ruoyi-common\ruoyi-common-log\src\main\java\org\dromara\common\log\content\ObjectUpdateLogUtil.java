package org.dromara.common.log.content;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 变更对象日志内容生成器
 * <AUTHOR>
 */
@Slf4j
public class ObjectUpdateLogUtil {


    /**
     * 生成变更记录
     * @return 变更记录
     */
    public List<String> generatorChangeLog(Object oldObject, Object newObject, String objectType, Class<?> entityClass) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        if (Objects.isNull(oldObject) || Objects.isNull(newObject)){
            log.error("异常信息:{}", "对比对象不能为null");
        }
        // 封装对象
        OperatorLogMetaDataBuilder builder = new OperatorLogMetaDataBuilder();
        OperatorLogMetaData oldOperatorLogMetaData = builder.getChangeModel(oldObject, entityClass);
        OperatorLogMetaData newOperatorLogMetaData = builder.getChangeModel(newObject, entityClass);
        return generatorChangeLog(oldOperatorLogMetaData, newOperatorLogMetaData, objectType, entityClass);
    }

    /**
     * 生成变更记录
     * @param oldOperatorLogMetaData
     * @param newOperatorLogMetaData
     * @param objectType
     * @param entityClass
     * @return
     * @throws IllegalAccessException
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     */
    public List<String> generatorChangeLog(OperatorLogMetaData oldOperatorLogMetaData, OperatorLogMetaData newOperatorLogMetaData, String objectType, Class<?> entityClass) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException  {
        List<String> result = new ArrayList<>();
        // 父类字段变更和引用子对象
        String filedChangeLog = getFiledChangeLog(oldOperatorLogMetaData, newOperatorLogMetaData, objectType, entityClass);
        if (isNoBlankString(filedChangeLog)){
            result.add(filedChangeLog);
        }
        // 复杂列表变更
        result.addAll(getComplexListChangeLogs(oldOperatorLogMetaData.getListMap(), newOperatorLogMetaData.getListMap(), entityClass));
        return result;
    }



    /**
     * 获取父类字段变更和引用子对象字段变更
     */
    private String getFiledChangeLog(OperatorLogMetaData oldOperatorLogMetaData, OperatorLogMetaData newOperatorLogMetaData, String objectType, Class<?> entityClass){
        Map<String,String> oldFieldMap = oldOperatorLogMetaData.getFieldMap(); // 旧数据map
        Map<String,String> newFieldMap = newOperatorLogMetaData.getFieldMap(); // 新数据map
        Map<String, OperatorLogMetaData> oldQuoteSubObject = oldOperatorLogMetaData.getSubObjectMap();
        Map<String, OperatorLogMetaData> newQuoteSubObject = newOperatorLogMetaData.getSubObjectMap();

        String objectName = oldOperatorLogMetaData.getName(); // 修改对象主键名称
        String objectId = oldOperatorLogMetaData.getId(); // 修改对象主键ID


        List<OperatorLogRecord> result = new ArrayList<>();
        // 父类字段变更
        if (Objects.nonNull(oldFieldMap) && Objects.nonNull(newFieldMap)) {
            for (Map.Entry<String,String> entry : oldFieldMap.entrySet()) {
                String newValue = newFieldMap.get(entry.getKey());
                if (Objects.nonNull(newValue) && !newValue.equals(entry.getValue())) {
                    String fieldContent = OperatorLogMetaDataBuilder.getColumnCommentAndDictValue(
                        entityClass,
                        entry.getKey(),
                        newValue
                    );
                    result.add(new OperatorLogRecord(fieldContent));
                }
            }
        }
        // 引用字段变更
        if (Objects.nonNull(oldQuoteSubObject) && Objects.nonNull(newQuoteSubObject)){
            for (Map.Entry<String, OperatorLogMetaData> entry : oldQuoteSubObject.entrySet()){
                OperatorLogMetaData newValue = newQuoteSubObject.get(entry.getKey());
                if (Objects.nonNull(newValue) && !newValue.getId().equals(entry.getValue().getId())){
                    result.add(new OperatorLogRecord(OperatorLogContentUtil.updateFiledFormat(entry.getValue().getName(), newValue.getName(), entry.getKey())));
                }
            }
        }

        if (!result.isEmpty()){
//            // 根据优先级排序
//            result.sort(java.util.Comparator.comparingInt(OperatorLogRecord::getPriority));
            return OperatorLogContentUtil.updateFormat(objectType, objectName, objectId, result.stream().map(OperatorLogRecord::getChangeLogContent).collect(Collectors.joining()));
        }
        return null;
    }


    /**
     * 获取复杂列表变更
     */
    private List<String> getComplexListChangeLogs(Map<String,List<OperatorLogMetaData>> oldListMap, Map<String,List<OperatorLogMetaData>> newListMap, Class<?> clazz) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        List<String> result = new ArrayList<>();
        if (Objects.isNull(oldListMap) || Objects.isNull(newListMap)){
            return result;
        }
        for (Map.Entry<String,List<OperatorLogMetaData>> entry : oldListMap.entrySet()){
            List<OperatorLogMetaData> newOperatorLogMetaData = newListMap.get(entry.getKey());
            if (Objects.nonNull(newOperatorLogMetaData)){
                result.addAll(getComplexListChangeLog(entry.getValue(), newOperatorLogMetaData,entry.getKey(), clazz));
            }
        }
        return result;
    }

    /**
     * 获取复杂列表变更
     */
    private List<String> getComplexListChangeLog(List<OperatorLogMetaData> oldOperatorLogMetaDatas, List<OperatorLogMetaData> newOperatorLogMetaDatas, String objectType, Class<?> entityClass) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        List<String> result = new ArrayList<>();
        if (Objects.isNull(oldOperatorLogMetaDatas) || Objects.isNull(newOperatorLogMetaDatas)){
            return result;
        }
        // 新增
        List<String> addObjectNames = newOperatorLogMetaDatas.stream().filter(i -> oldOperatorLogMetaDatas.stream()
                .noneMatch(j -> j.getId().equals(i.getId()))).map(OperatorLogMetaData::getName).collect(Collectors.toList());
        if (isNoBlankList(addObjectNames)){
            result.add(OperatorLogContentUtil.addBatchFieldFormat(addObjectNames, objectType));
        }
        // 删除
        List<String> deleteObjectNames = oldOperatorLogMetaDatas.stream().filter(i -> newOperatorLogMetaDatas.stream()
                .noneMatch(j -> j.getId().equals(i.getId()))).map(OperatorLogMetaData::getName).collect(Collectors.toList());
        if (isNoBlankList(deleteObjectNames)){
            result.add(OperatorLogContentUtil.deleteBatchFieldFormat(deleteObjectNames, objectType));
        }
        // 更新
        for (OperatorLogMetaData oldOperatorLogMetaData : oldOperatorLogMetaDatas){
            for (OperatorLogMetaData newOperatorLogMetaData : newOperatorLogMetaDatas){
                if (oldOperatorLogMetaData.getId().equals(newOperatorLogMetaData.getId())){
                    result.addAll(generatorChangeLog(oldOperatorLogMetaData, newOperatorLogMetaData,objectType, entityClass));
                }
            }
        }
        return result;
    }


    /**
     * 判断字符串是否为null或空字符
     */
    public boolean isNoBlankString(String str){
        return Objects.nonNull(str) && str.length() > 0;
    }

    /**
     * 判断列表是否为null或大小为0
     */
    public boolean isNoBlankList(List<?>  list){
        return Objects.nonNull(list) && !list.isEmpty();
    }

}
