package org.dromara.cssrcSoftware.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.dromara.cssrcSoftware.domain.SoftwareCommentLike;
import org.dromara.cssrcSoftware.domain.vo.SoftwareCommentLikeVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 软件点赞关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@DS("software")
public interface SoftwareCommentLikeMapper extends BaseMapperPlus<SoftwareCommentLike, SoftwareCommentLikeVo> {

}
