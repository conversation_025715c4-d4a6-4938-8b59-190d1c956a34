package org.dromara.common.log.content;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 删除对象日志内容生成器
 * <AUTHOR>
 */
public class ObjectDeleteLogUtil {

    public String generatorContent(Object object, String objectType, String objectId, Class<?> entityClass) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        OperatorLogMetaDataBuilder builder = new OperatorLogMetaDataBuilder();
        OperatorLogMetaData metaData = builder.getChangeModel(object, entityClass);

        List<String> fieldContents = new ArrayList<>();
        Map<String, String> fieldMap = metaData.getFieldMap();

        for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
            String fieldContent = OperatorLogMetaDataBuilder.getColumnCommentAndDictValue(
                entityClass,
                entry.getKey(),
                entry.getValue()
            );
            fieldContents.add(fieldContent);
        }

        String content = String.join(";", fieldContents);

        // 优先使用displayName，如果没有则使用name
        String displayName = metaData.getDisplayName();
        if (displayName == null) {
            displayName = metaData.getName();
        }

        return OperatorLogContentUtil.deleteObjFormat(displayName, objectType, objectId, content);
    }

    private String formatValue(Object value) {
        if (value == null) {
            return "null";
        }
        if (value.getClass().isArray()) {
            if (value instanceof Long[]) {
                return Arrays.toString((Long[]) value);
            }
            return Arrays.toString((Object[]) value);
        }
        return value.toString();
    }

}
