package org.dromara.system.mapper;

import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.system.domain.SysNoticeOss;

import java.util.List;

/**
 * 用户与角色关联表 数据层
 *
 * <AUTHOR> Li
 */
public interface SysNoticeOssMapper extends BaseMapperPlus<SysNoticeOss, SysNoticeOss> {

    List<Long> selectOssIdsByNoticeId(Long noticeId);

    List<SysNoticeOss> selectNoticeOssByNoticeId(Long noticeId);

    int increaseDownloadCount(Long noticeId, Long ossId);

    int getDownloadCount(Long noticeId, Long ossId);

}
