package org.dromara.common.core.service;

import org.dromara.common.core.domain.dto.CompleteTaskDTO;
import org.dromara.common.core.domain.dto.StartProcessDTO;
import org.dromara.common.core.domain.dto.StartProcessReturnDTO;

import java.util.List;
import java.util.Map;

/**
 * 通用 工作流服务
 *
 * <AUTHOR>
 */
public interface WorkflowService {

    /**
     * 运行中的实例 删除程实例，删除历史记录，删除业务与流程关联信息
     *
     * @param businessIds 业务id
     * @return 结果
     */
    boolean deleteInstance(List<Long> businessIds);

    /**
     * 获取当前流程状态
     *
     * @param taskId 任务id
     * @return 状态
     */
    String getBusinessStatusByTaskId(Long taskId);

    /**
     * 获取当前流程状态
     *
     * @param businessId 业务id
     * @return 状态
     */
    String getBusinessStatus(String businessId);

    /**
     * 设置流程变量
     *
     * @param instanceId 流程实例id
     * @param variable   流程变量
     */
    void setVariable(Long instanceId, Map<String, Object> variable);

    /**
     * 获取流程变量
     *
     * @param instanceId 流程实例id
     */
    Map<String, Object> instanceVariable(Long instanceId);

    /**
     * 按照业务id查询流程实例id
     *
     * @param businessId 业务id
     * @return 结果
     */
    Long getInstanceIdByBusinessId(String businessId);

    /**
     * 新增租户流程定义
     *
     * @param tenantId 租户id
     */
    void syncDef(String tenantId);

    /**
     * 启动流程
     *
     * @param startProcess 参数
     * @return 结果
     */
    StartProcessReturnDTO startWorkFlow(StartProcessDTO startProcess);

    /**
     * 办理任务
     * 系统后台发起审批 无用户信息 需要忽略权限
     * completeTask.getVariables().put("ignore", true);
     *
     * @param completeTask 参数
     */
    boolean completeTask(CompleteTaskDTO completeTask);

    /**
     * 办理任务
     *
     * @param taskId  任务ID
     * @param message 办理意见
     */
    boolean completeTask(Long taskId, String message);

    /**
     * 根据当前用户所在部门获取和角色中和用户同部门用户作为审批用户
     *
     * @param userId
     * 用户ID:从流程定义文件传入，spel表达式示例:#initiator（发起用户）参数都是用variable中获取
     * 比如前文代码中的：
     *         // 启动流程实例（提交申请）
     *         Map<String, Object> variables = startProcessBo.getVariables();
     *         // 流程发起人
     *         variables.put(INITIATOR, LoginHelper.getUserIdStr());
     *         // 业务id
     *         variables.put(BUSINESS_ID, businessId);
     *
     * @param level
     * 处理层级：目前支持枚举“部门”“班组”，直接写成字符串放入流程spel表达式
     *
     * @param roleName
     * 角色名称：需要和角色表中的名称一一对应，直接写成字符串放入流程spel表达式
     *
     * @return
     */
    String selectApproveUserIdByDeptAndRole(String userId, String level, String roleName);
}
