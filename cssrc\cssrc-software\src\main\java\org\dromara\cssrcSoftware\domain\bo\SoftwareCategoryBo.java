package org.dromara.cssrcSoftware.domain.bo;

import org.dromara.cssrcSoftware.domain.SoftwareCategory;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 软件分类业务对象 software_category
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SoftwareCategory.class, reverseConvertGenerate = false)
public class SoftwareCategoryBo extends BaseEntity {

    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空", groups = { EditGroup.class })
    private Long categoryId;

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String categoryName;

    /**
     * 父级分类ID
     */
    private Long parentId;

    /**
     * 祖级分类路径
     */
    private String ancestors;

    /**
     * 显示顺序
     */
    @NotNull(message = "显示顺序不能为空")
    private Integer orderNum;

    /**
     * 所属范围
     */
    private String softwareRange;

    /**
     * 状态（0正常 1停用）
     */
    private String status;


}
