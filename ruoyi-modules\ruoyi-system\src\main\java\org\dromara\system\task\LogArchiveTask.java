package org.dromara.system.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.system.service.ISysLogArchiveService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 日志转存定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LogArchiveTask {

    private final ISysLogArchiveService logArchiveService;

    /**
     * 每周日凌晨2点执行日志转存检查
     */
    @Scheduled(cron = "0 0 2 ? * SUN")
//    @Scheduled(cron = "0 * * * * ?")
    public void executeLogArchive() {
        try {
            log.info("开始执行日志转存检查任务");
            logArchiveService.checkAndArchiveLogs();
            log.info("日志转存检查任务执行完成");
        } catch (Exception e) {
            log.error("日志转存任务执行失败", e);
        }
    }


}
