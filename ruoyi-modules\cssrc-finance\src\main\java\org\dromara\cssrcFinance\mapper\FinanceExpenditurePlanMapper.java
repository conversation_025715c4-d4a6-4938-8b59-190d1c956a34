package org.dromara.cssrcFinance.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.cssrcFinance.domain.FinanceExpenditurePlan;
import org.dromara.cssrcFinance.domain.vo.FinanceExpenditurePlanVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.Collection;

/**
 * 财务-预算-支出计划Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
public interface FinanceExpenditurePlanMapper extends BaseMapperPlus<FinanceExpenditurePlan, FinanceExpenditurePlanVo> {
    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<FinanceExpenditurePlanVo>> P selectVoPage(IPage<FinanceExpenditurePlan> page, Wrapper<FinanceExpenditurePlan> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }


    @Override
    @DataPermission({
        @DataColumn(key = "userName", value = "create_by")
    })
    int updateById(@Param(Constants.ENTITY) FinanceExpenditurePlan update);

    @Override
    @DataPermission({
        @DataColumn(key = "userName", value = "create_by")
    })
    default int deleteByIds(@Param(Constants.COLL) Collection<?> idList) {
        return this.deleteByIds(idList, true);
    }

}
