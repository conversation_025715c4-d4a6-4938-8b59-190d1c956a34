package org.dromara.system.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户审批申请视图对象 sys_user_approval
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
public class SysUserApprovalVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 审批ID
     */
    @ExcelProperty(value = "审批ID")
    private Long approvalId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 审批类型
     */
    @ExcelProperty(value = "审批类型")
    private String approvalType;

    /**
     * 审批数据
     */
    private String approvalData;

    /**
     * 审批状态
     */
    @ExcelProperty(value = "审批状态")
    private String approvalStatus;

    /**
     * 流程实例ID
     */
    @ExcelProperty(value = "流程实例ID")
    private Long flowInstanceId;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}
