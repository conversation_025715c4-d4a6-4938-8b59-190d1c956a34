package org.dromara.cssrcSoftware.domain.vo;

import org.dromara.common.encrypt.annotation.EncryptField;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.cssrcSoftware.domain.SoftwareVersion;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 软件版本详情视图对象 software_version
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SoftwareVersion.class)
public class SoftwareVersionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 版本ID
     */
    @ExcelProperty(value = "版本ID")
    private Long versionId;

    /**
     * 软件ID
     */
    @ExcelProperty(value = "软件ID")
    private Long softwareId;

    /**
     * 版本号（如 v1.0.0）
     */
    @ExcelProperty(value = "版本号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=,v=1.0.0")
    private String versionName;

    /**
     * 平台（windows/linux）
     */
    @ExcelProperty(value = "平台", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "windows/linux")
    private String platform;

    /**
     * 架构（x86/x64/arm64）
     */
    @ExcelProperty(value = "架构", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "x86/x64/arm64")
    private String architecture;

    /**
     * 包类型（exe/msi/deb/rpm/tar.gz）
     */
    @ExcelProperty(value = "包类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "exe/msi/deb/rpm/tar.gz")
    private String packageType;

    /**
     * 位数（32/64）
     */
    @ExcelProperty(value = "位数", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "32/64")
    private String bits;

    /**
     * 文件id
     */
    @ExcelProperty(value = "文件id")
    private Long ossId;

    /**
     * 文件大小(GB)
     */
    @ExcelProperty(value = "文件大小(GB)")
    private Double fileSize;

    /**
     * 版本文件名称
     */
    @ExcelProperty(value = "版本文件名称")
    private String versionFileName;

    /**
     * 版本文件密级
     */
    @ExcelProperty(value = "版本文件密级")
    private String versionFileSecret;

    /**
     * 版本文件URL
     */
    @ExcelProperty(value = "版本文件url")
    private String versionFileUrl;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 审批状态
     */
    @ExcelProperty(value = "审批状态")
    private String approvalStatus;

    /**
     * 前台显示状态（0启用 1禁用）
     */
    @ExcelProperty(value = "前台显示状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=启用,1=禁用")
    private String displayStatus;

    /**
     * 状态（0正常 1停用）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 下载次数
     */
    @ExcelProperty(value = "下载次数")
    private Long downloadCount;

    /**
     * 版本密级
     */
    @EncryptField
    @ExcelProperty(value = "版本密级", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_file_secret")
    private String secret;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建人工号
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;

    /**
     * 创建人名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "createBy")
    private String createByNickName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
