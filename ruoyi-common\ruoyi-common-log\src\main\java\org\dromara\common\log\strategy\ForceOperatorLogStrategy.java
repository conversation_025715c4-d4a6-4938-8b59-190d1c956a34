package org.dromara.common.log.strategy;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.core.domain.dto.UserOnlineDTO;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.log.content.OperatorLogMetaData;
import org.dromara.common.log.content.OperatorLogMetaDataBuilder;
import org.dromara.common.log.utils.OperatorLogContext;
import org.dromara.common.log.utils.OperatorLogQueryUtil;
import org.dromara.common.log.utils.OperatorLogUtil;
import org.dromara.common.redis.utils.RedisUtils;
import org.springframework.stereotype.Component;

import java.util.Base64;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 强退操作日志生成策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ForceOperatorLogStrategy implements IOperateLogStrategy {

    @Override
    public String getOperatorContent(Object requestObj, Class<?>[] tableEntity, String operatorObjId) {
        try {
            // operatorObjId 是 tokenId
            String tokenId = operatorObjId;
            
            if (StringUtils.isEmpty(tokenId)) {
                return "执行了[强退]操作，但未指定会话ID";
            }

            // 判断是强退他人还是强退自己的设备
            String methodName = getCurrentMethodName();
            boolean isSelfDevice = methodName.contains("myself") || methodName.contains("remove");
            
            // 优先从Redis中获取在线用户信息
            UserOnlineDTO userOnlineInfo = getUserOnlineInfo(tokenId);
            String targetUserInfo = null;
            String deviceInfo = null;

            if (userOnlineInfo != null) {
                targetUserInfo = getUserDisplayNameByUserName(userOnlineInfo.getUserName());
                deviceInfo = buildDeviceInfo(userOnlineInfo);
            } else {
                // 如果Redis中没有，尝试从token中解析用户信息
                targetUserInfo = parseUserInfoFromToken(tokenId);
            }

            // 构建日志内容
            if (StringUtils.isNotEmpty(targetUserInfo)) {
                if (isSelfDevice) {
                    if (StringUtils.isNotEmpty(deviceInfo)) {
                        return String.format("强退自己的设备：%s，设备信息[%s]", targetUserInfo, deviceInfo);
                    } else {
                        return String.format("强退自己的设备：%s", targetUserInfo);
                    }
                } else {
                    if (StringUtils.isNotEmpty(deviceInfo)) {
                        return String.format("强退用户：%s，设备信息[%s]", targetUserInfo, deviceInfo);
                    } else {
                        return String.format("强退用户：%s", targetUserInfo);
                    }
                }
            } else {
                // 如果无法获取用户信息，使用简化描述
                if (isSelfDevice) {
                    return "强退自己的设备";
                } else {
                    return "强退用户";
                }
            }
        } catch (Exception e) {
            log.error("生成强退操作日志失败", e);
            return "执行了[强退]操作，会话ID：" + operatorObjId;
        } finally {
            OperatorLogContext.clear();
        }
    }

    /**
     * 获取当前方法名
     */
    private String getCurrentMethodName() {
        try {
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            for (StackTraceElement element : stackTrace) {
                String className = element.getClassName();
                if (className.contains("Controller")) {
                    return element.getMethodName();
                }
            }
        } catch (Exception e) {
            log.debug("获取方法名失败", e);
        }
        return "";
    }

    /**
     * 从Redis中获取在线用户信息
     */
    private UserOnlineDTO getUserOnlineInfo(String tokenId) {
        try {
            String cacheKey = CacheConstants.ONLINE_TOKEN_KEY + tokenId;
            return RedisUtils.getCacheObject(cacheKey);
        } catch (Exception e) {
            log.debug("获取在线用户信息失败: tokenId={}", tokenId, e);
            return null;
        }
    }

    /**
     * 构建设备信息字符串
     */
    private String buildDeviceInfo(UserOnlineDTO userOnline) {
        StringBuilder deviceInfo = new StringBuilder();
        
        if (StringUtils.isNotEmpty(userOnline.getIpaddr())) {
            deviceInfo.append("IP:").append(userOnline.getIpaddr());
        }
        
        if (StringUtils.isNotEmpty(userOnline.getLoginLocation())) {
            if (deviceInfo.length() > 0) deviceInfo.append(", ");
            deviceInfo.append("地址:").append(userOnline.getLoginLocation());
        }
        
        if (StringUtils.isNotEmpty(userOnline.getBrowser())) {
            if (deviceInfo.length() > 0) deviceInfo.append(", ");
            deviceInfo.append("浏览器:").append(userOnline.getBrowser());
        }
        
        if (StringUtils.isNotEmpty(userOnline.getOs())) {
            if (deviceInfo.length() > 0) deviceInfo.append(", ");
            deviceInfo.append("系统:").append(userOnline.getOs());
        }
        
        if (StringUtils.isNotEmpty(userOnline.getDeviceType())) {
            if (deviceInfo.length() > 0) deviceInfo.append(", ");
            deviceInfo.append("设备:").append(userOnline.getDeviceType());
        }
        
        return deviceInfo.length() > 0 ? deviceInfo.toString() : "未知设备";
    }

    /**
     * 通过用户名获取用户显示名称
     */
    private String getUserDisplayNameByUserName(String userName) {
        try {
            if (StringUtils.isEmpty(userName)) {
                return "未知用户";
            }

            // 通过用户名查询用户信息，这里需要调用用户服务
            // 由于OperatorLogQueryUtil.getObj只支持通过ID查询，我们需要另想办法
            return getUserInfoByUserName(userName);
        } catch (Exception e) {
            log.debug("获取用户显示名称失败: userName={}", userName, e);
            return userName != null ? userName : "未知用户";
        }
    }

    /**
     * 通过用户名获取用户信息
     */
    private String getUserInfoByUserName(String userName) {
        try {
            // 这里可以通过Spring上下文获取用户服务来查询
            // 但为了避免循环依赖，我们采用简化的方式
            return userName;
        } catch (Exception e) {
            log.debug("通过用户名获取用户信息失败: userName={}", userName, e);
            return userName;
        }
    }

    /**
     * 从token中解析用户信息
     */
    private String parseUserInfoFromToken(String tokenId) {
        try {
            if (StringUtils.isEmpty(tokenId)) {
                return null;
            }

            // JWT token格式：header.payload.signature
            if (tokenId.contains(".")) {
                String[] parts = tokenId.split("\\.");
                if (parts.length >= 2) {
                    // 解析payload部分
                    String payload = parts[1];
                    try {
                        // Base64解码
                        byte[] decodedBytes = Base64.getDecoder().decode(payload);
                        String decodedPayload = new String(decodedBytes);

                        // JSON解析
                        ObjectMapper mapper = new ObjectMapper();
                        JsonNode jsonNode = mapper.readTree(decodedPayload);

                        // 提取用户信息
                        String userName = jsonNode.has("userName") ? jsonNode.get("userName").asText() : null;
                        String nickName = jsonNode.has("nickName") ? jsonNode.get("nickName").asText() : null;

                        if (StringUtils.isNotEmpty(nickName) && StringUtils.isNotEmpty(userName)) {
                            return String.format("%s(%s)", nickName, userName);
                        } else if (StringUtils.isNotEmpty(userName)) {
                            return userName;
                        } else if (StringUtils.isNotEmpty(nickName)) {
                            return nickName;
                        }
                    } catch (Exception e) {
                        log.debug("解析token payload失败", e);
                    }
                }
            }

            return null;
        } catch (Exception e) {
            log.debug("从token解析用户信息失败: tokenId={}", tokenId, e);
            return null;
        }
    }
}
