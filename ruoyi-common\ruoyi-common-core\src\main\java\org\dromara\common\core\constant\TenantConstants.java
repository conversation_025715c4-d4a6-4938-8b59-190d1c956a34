package org.dromara.common.core.constant;

/**
 * 租户常量信息
 *
 * <AUTHOR>
 */
public interface TenantConstants {

    /**
     * 超级管理员ID
     */
    Long SUPER_ADMIN_ID = 1L;

    /**
     * 超级管理员角色 roleKey
     */
    String SUPER_ADMIN_ROLE_KEY = "superadmin";

    /**
     * 租户管理员角色 roleKey
     */
    String TENANT_ADMIN_ROLE_KEY = "admin";

    /**
     * 系统管理员角色
     */
    String SYSTEM_ROLE_KEY = "system";

    /**
     * 安全管理员角色
     */
    String RIGHT_ROLE_KEY = "right";

    /**
     * 审计员角色
     */
    String CHECK_ROLE_KEY = "check";

    /**
     * 平台管理员，开发人员
     */
    String OPERATOR_ROLE_KEY = "operator";

    /**
     * 租户管理员角色名称
     */
    String TENANT_ADMIN_ROLE_NAME = "管理员";

    /**
     * 默认租户ID
     */
    String DEFAULT_TENANT_ID = "000000";

}
