package org.dromara.cssrcSoftware.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 软件下载日志对象 software_download_log
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("software_download_log")
@Schema(description = "软件下载日志")
public class SoftwareDownloadLog extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    @TableId(value = "log_id")
    private Long logId;

    /**
     * 软件ID
     */
    private Long softwareId;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 下载用户ID
     */
    private Long userId;

    /**
     * 下载IP
     */
    private String ipAddress;

    /**
     * 下载时间
     */
    private Date downloadTime;

    /**
     * 下载状态
     */
    private String status;


}
