package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 通知公告用户阅读表 sys_notice_user
 *
 * <AUTHOR>
 */

@Data
@TableName("sys_notice_user")
public class SysNoticeUser {

    /**
     * 通知公告ID
     */
    @TableId(type = IdType.INPUT)
    private Long noticeId;

    /**
     * 用户ID
     */
//    @TableId(value = "user_id")
    private Long userId;

    /**
     * 阅读时间
     */
    private Date readTime;

}
