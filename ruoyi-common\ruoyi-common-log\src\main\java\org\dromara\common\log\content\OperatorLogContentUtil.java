package org.dromara.common.log.content;

import java.util.List;

/**
 * 操作日志内容格式定义
 * <AUTHOR>
 */
public class OperatorLogContentUtil {

    /**
     * 批量新增子对象格式
     */
    public static String addBatchSubObjFormat(String addSubObjNames, String addObjType, String objName, String objType) {
        return String.format("%s[%s]新增：%s[%s]", objType, objName, addObjType, addSubObjNames);
    }

    /**
     * 列表字段新增格式
     */
    public static String addBatchFieldFormat(List<String> addObjectName, String objectType){
        return String.format("新增%s：%s", objectType,  String.join(", ", addObjectName));
    }


    /**
     * 列表字段删除格式
     */
    public static String deleteBatchFieldFormat(List<String> addObjectName, String objectType){
        return String.format("删除%s：%s", objectType,  String.join(", ", addObjectName));
    }

    /**
     * 新增时成员格式
     * @param filedName
     * @param filedValue
     * @return
     */
    public static String addFiledFormat(String filedName, String filedValue) {
        return String.format("%s：%s；", filedName, filedValue);
    }


    /**
     * 添加对象格式
     * @param objName
     * @param objectType
     * @param content
     * @return
     */
    public static String addObjFormat(String objName, String objectType, String content) {
        return String.format("新增%s[%s]： %s", objectType, objName, content);
    }


    /**
     * 更新字段格式
     * @param oldValue
     * @param newValue
     * @param fieldName
     * @return
     */
    public static String updateFiledFormat(String oldValue, String newValue, String fieldName){
        return String.format("%s： %s --> %s", fieldName, oldValue , newValue + "；");
    }

    /**
     * 更新格式
     * @param objectType
     * @param objectName
     * @param objectId
     * @return
     */
    public static String updateFormat(String objectType, String objectName, String objectId, String content) {
        return String.format("更新%s[%s:%s]: %s", objectType, objectName, objectId, content);
    }


    /**
     * 删除时字段格式
     */
    public static String deleteFiledFormat(String filedName, String filedValue) {
        return String.format("%s：%s；", filedName, filedValue);
    }

    /**
     * 删除对象格式
     */
    public static String deleteObjFormat(String objName, String objectType, String objectId, String content) {
        return String.format("删除%s[%s:%s]： %s", objectType, objName, objectId, content);
    }

    /**
     * 批量删除对象格式
     */
    public static String deleteBatchObjFormat(List<String> objNames, String objectType, List<String> objectIds, String content) {
        return String.format("批量删除%s[%s:%s]： %s", objectType, String.join("，", objNames), String.join("，", objectIds), content);
    }

    /**
     * 下载对象格式
     */
    public static String downloadObjFormat(String objName, String objectType, String content) {
        return String.format("下载%s[%s]： %s", objectType, objName, content);
    }

    /**
     * 授权对象格式
     */
    public static String grantObjFormat(String objName, String objectType, String content) {
        return String.format("授权%s[%s]： %s", objectType, objName, content);
    }

    /**
     * 取消授权对象格式
     */
    public static String cancelGrantObjFormat(String objName, String objectType, String content) {
        return String.format("取消授权%s[%s]： %s", objectType, objName, content);
    }

    /**
     * 批量授权对象格式
     */
    public static String grantBatchObjFormat(List<String> objNames, String objectType, List<String> objectIds, String content) {
        return String.format("批量授权%s[%s:%s]： %s", objectType, String.join("，", objNames), String.join("，", objectIds), content);
    }

    /**
     * 批量取消授权对象格式
     */
    public static String cancelGrantBatchObjFormat(List<String> objNames, String objectType, List<String> objectIds, String content) {
        return String.format("批量取消授权%s[%s:%s]： %s", objectType, String.join("，", objNames), String.join("，", objectIds), content);
    }

}
