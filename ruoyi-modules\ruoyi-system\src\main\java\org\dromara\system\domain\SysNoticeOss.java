package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 通知公告和文件关联 sys_notice_oss
 *
 * <AUTHOR>
 */

@Data
@TableName("sys_notice_oss")
public class SysNoticeOss {

    /**
     * 通知公告ID
     */
    @TableId(type = IdType.INPUT)
    private Long noticeId;

    /**
     * 文件ID
     */
    private Long ossId;

    /**
     * 下载次数
     */
    private int downloadCount;

}
