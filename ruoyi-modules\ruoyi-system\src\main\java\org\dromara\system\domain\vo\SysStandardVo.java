package org.dromara.system.domain.vo;

import org.dromara.common.encrypt.annotation.EncryptField;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.system.domain.SysStandard;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 标准视图对象 sys_standard
 *
 * <AUTHOR> Li
 * @date 2025-06-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysStandard.class)
public class SysStandardVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 标准id
     */
    @ExcelProperty(value = "标准id")
    private Long standardId;

    /**
     * 标准编号
     */
    @ExcelProperty(value = "标准编号")
    private String standardCode;

    /**
     * 标准名称
     */
    @ExcelProperty(value = "标准名称")
    private String standardName;

    /**
     * 密级
     */
    @ExcelProperty(value = "标准密级", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_file_secret")
    @EncryptField
    private String secret;

    /**
     * 标准状态（0正常 1停用）
     */
    @ExcelProperty(value = "标准状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=正常,1=停用")
    private String standardStatus;

    /**
     * 标准文件
     */
//    @ExcelProperty(value = "标准文件")
    private Long standardFile;

    /**
     * 标准文件名称
     */
    @ExcelProperty(value = "标准文件名称")
    private String standardFileName;

    /**
     * 标准文件密级
     */
    @ExcelProperty(value = "标准文件密级")
    private String standardFileSecret;

    /**
     * 标准文件url
     */
    private String standardFileUrl;

    /**
     * 发布时间
     */
    private Date updateTime;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建部门名称
     */
    @Translation(type = TransConstant.DEPT_ID_TO_NAME, mapper = "createDept")
    private String createDeptName;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "createBy")
    private String createByNickName;

}
