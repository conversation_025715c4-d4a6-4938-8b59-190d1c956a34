package org.dromara.web.service.impl;

import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.domain.model.CertLoginBody;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.enums.LoginType;
import org.dromara.common.core.exception.user.UserException;
import org.dromara.common.core.utils.ValidatorUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.vo.SysClientVo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.mapper.SysUserMapper;
import org.dromara.web.domain.vo.LoginVo;
import org.dromara.web.service.IAuthStrategy;
import org.dromara.web.service.SysLoginService;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

/**
 * 证书认证策略
 *
 * <AUTHOR>
 */
@Slf4j
@Service("certificate" + IAuthStrategy.BASE_NAME)
@RequiredArgsConstructor
public class CertificateAuthStrategy implements IAuthStrategy {
    private final SysUserMapper userMapper;
    private final SysLoginService loginService;


    @Override
    public LoginVo login(HttpServletRequest request, String body, SysClientVo client) {
        // 解析请求体中的证书信息（假设前端传递）
        CertLoginBody loginBody = JsonUtils.parseObject(body, CertLoginBody.class);
        // 从Cookie中填充数据
        getInfoFromCookie(loginBody, request);
        // 验证数据信息是否完整
        ValidatorUtils.validate(loginBody);

        // 填充租户Id和用户工号
        String tenantId = loginBody.getTenantId();
        String username = loginBody.getUsername();

        // 验证IP地址
//        validateClientIp(user, certBody.getClientIp());


        LoginUser loginUser = TenantHelper.dynamic(tenantId, () -> {
            SysUserVo user = loadUserByUsername(username);
            loginService.checkLogin(LoginType.PASSWORD, tenantId, username, () -> false);
            // 此处可根据登录用户的数据不同 自行创建 loginUser
            return loginService.buildLoginUser(user);
        });
        loginUser.setClientKey(client.getClientKey());
        loginUser.setDeviceType(client.getDeviceType());
        SaLoginModel model = new SaLoginModel();
        model.setDevice(client.getDeviceType());
        // 自定义分配 不同用户体系 不同 token 授权时间 不设置默认走全局 yml 配置
        // 例如: 后台用户30分钟过期 app用户1天过期
        model.setTimeout(client.getTimeout());
        model.setActiveTimeout(client.getActiveTimeout());
        model.setExtra(LoginHelper.CLIENT_KEY, client.getClientId());
        // 生成token
        LoginHelper.login(loginUser, model);

        LoginVo loginVo = new LoginVo();
        loginVo.setAccessToken(StpUtil.getTokenValue());
        loginVo.setExpireIn(StpUtil.getTokenTimeout());
        loginVo.setClientId(client.getClientId());
        return loginVo;
    }

    private SysUserVo loadUserByUsername(String username) {
        SysUserVo user = userMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, username));
        if (ObjectUtil.isNull(user)) {
            log.info("登录用户：{} 不存在.", username);
            throw new UserException("user.not.exists", username);
        } else if (SystemConstants.DISABLE.equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", username);
            throw new UserException("user.blocked", username);
        }
        return user;
    }

//    private void validateClientIp(SysUser user, String clientIp) {
//        // 验证客户端IP是否在白名单内（示例）
//        if (!StringUtils.contains(user.getAllowedIps(), clientIp)) {
//            throw new UserException("ip.not.authorized");
//        }
//    }

    /**
     * 从Cookie中填充数据
     *
     * @param loginBody
     * @param request
     */
    private void getInfoFromCookie(CertLoginBody loginBody, HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("KOAL_CERT_T".equals(cookie.getName())) {
                    String value = cookie.getValue();
                    try {
                        // 按官方示例进行解码和字符集转换（注意顺序）
                        String decoded = URLDecoder.decode(value, "UTF-8"); // 先 URL 解码
                        // 如果服务器端需要 GBK 编码的中文字符，可能需要转换
                        byte[] isoBytes = decoded.getBytes("ISO-8859-1");
                        loginBody.setUsername(new String(isoBytes, "GBK"));

//                        loginBody.setUsername(value);
                    } catch (UnsupportedEncodingException e) {
                        log.error("解码 KOAL_CERT_CN 失败", e);
                    }
                }
            }
        }

//        loginBody.setUsername("081583");
    }

}
