package org.dromara.system.domain.vo;

import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.system.domain.SysUserRole;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户和角色关联视图对象 sys_user_role
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysUserRole.class)
public class SysUserRoleVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 角色ID
     */
    @ExcelProperty(value = "角色ID")
    private Long roleId;

}
