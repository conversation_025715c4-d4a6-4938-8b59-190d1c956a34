package org.dromara.cssrcSoftware.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.cssrcSoftware.domain.SoftwareWhiteuser;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 软件权限白名单视图对象 software_whiteuser
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SoftwareWhiteuser.class)
public class SoftwareWhiteuserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 软件ID
     */
    @ExcelProperty(value = "软件ID")
    private Long softwareId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 授权原因
     */
    @ExcelProperty(value = "授权原因")
    private String grantReason;

    /**
     * 授权时间
     */
    @ExcelProperty(value = "授权时间")
    private Date grantTime;

    /**
     * 过期时间（可选）
     */
    @ExcelProperty(value = "过期时间", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "可=选")
    private Date expireTime;


}
