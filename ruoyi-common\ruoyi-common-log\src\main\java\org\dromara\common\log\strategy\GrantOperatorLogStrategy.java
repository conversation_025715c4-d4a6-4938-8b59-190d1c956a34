package org.dromara.common.log.strategy;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.log.content.*;
import org.dromara.common.log.utils.OperatorLogContext;
import org.dromara.common.log.utils.OperatorLogQueryUtil;
import org.dromara.common.log.utils.OperatorLogUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 授权操作日志生成策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GrantOperatorLogStrategy implements IOperateLogStrategy {

    @Override
    public String getOperatorContent(Object requestObj, Class<?>[] tableEntity, String operatorObjId) {
        try {
            // 判断是否为取消授权操作（通过方法名或其他标识）
            boolean isCancelGrant = isCancelGrantOperation();

            // 判断是用户授权角色还是角色授权用户
            String methodName = getCurrentMethodName();

            if (methodName.contains("authRole") || methodName.contains("AuthRole")) {
                // 用户授权角色场景：operatorObjId是userId，requestObj是roleIds
                return buildUserGrantRoleLog(operatorObjId, requestObj, isCancelGrant);
            } else if (methodName.contains("authUser") || methodName.contains("AuthUser")) {
                // 角色授权用户场景
                if (methodName.equals("cancelAuthUser")) {
                    // 单个取消授权：operatorObjId是userId，requestObj是roleId
                    return buildRoleGrantUserLog(convertRequestObjToString(requestObj), operatorObjId, isCancelGrant);
                } else {
                    // 批量授权/取消授权：operatorObjId是userIds，requestObj是roleId
                    return buildRoleGrantUserLog(convertRequestObjToString(requestObj), operatorObjId, isCancelGrant);
                }
            } else {
                // 通用授权场景
                String action = isCancelGrant ? "取消授权" : "授权";
                return String.format("进行了[%s]操作", action);
            }
        } catch (Exception e) {
            log.error("生成授权操作日志失败", e);
            return "生成授权日志失败:" + e.getMessage();
        } finally {
            OperatorLogContext.clear();
        }
    }

    /**
     * 获取当前方法名
     */
    private String getCurrentMethodName() {
        try {
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            for (StackTraceElement element : stackTrace) {
                String className = element.getClassName();
                if (className.contains("Controller")) {
                    return element.getMethodName();
                }
            }
        } catch (Exception e) {
            log.debug("获取方法名失败", e);
        }
        return "";
    }

    /**
     * 判断是否为取消授权操作
     */
    private boolean isCancelGrantOperation() {
        try {
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            for (StackTraceElement element : stackTrace) {
                String methodName = element.getMethodName();
                if (methodName.contains("cancel") || methodName.contains("delete") || methodName.contains("remove")) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.debug("判断授权操作类型失败", e);
        }
        return false;
    }

    /**
     * 构建用户授权角色日志
     */
    private String buildUserGrantRoleLog(String userId, Object roleIdsObj, boolean isCancelGrant) {
        try {
            String userDisplayName = getUserDisplayName(Long.parseLong(userId));
            String action = isCancelGrant ? "取消授权" : "授权";

            if (roleIdsObj instanceof Long[]) {
                Long[] roleIds = (Long[]) roleIdsObj;
                List<String> roleNames = new ArrayList<>();
                for (Long roleId : roleIds) {
                    roleNames.add(getRoleDisplayName(roleId));
                }
                return String.format("为用户[%s]%s角色：%s", userDisplayName, action, String.join("、", roleNames));
            } else {
                return String.format("为用户[%s]进行[%s]操作", userDisplayName, action);
            }
        } catch (Exception e) {
            log.error("构建用户授权角色日志失败", e);
            return String.format("用户ID[%s]进行授权操作", userId);
        }
    }

    /**
     * 构建角色授权用户日志
     */
    private String buildRoleGrantUserLog(String roleIdStr, String userIdsStr, boolean isCancelGrant) {
        try {
            Long roleId = Long.parseLong(roleIdStr);
            String roleDisplayName = getRoleDisplayName(roleId);
            String action = isCancelGrant ? "取消授权" : "授权";

            // 解析用户ID
            List<Long> userIds = parseUserIds(userIdsStr);
            if (userIds.size() > 0) {
                List<String> userNames = new ArrayList<>();
                for (Long userId : userIds) {
                    userNames.add(getUserDisplayName(userId));
                }
                return String.format("为角色[%s]%s用户：%s", roleDisplayName, action, String.join("、", userNames));
            } else {
                return String.format("为角色[%s]进行[%s]操作", roleDisplayName, action);
            }
        } catch (Exception e) {
            log.error("构建角色授权用户日志失败", e);
            return String.format("角色ID[%s]进行授权操作", roleIdStr);
        }
    }

    /**
     * 解析用户ID字符串
     */
    private List<Long> parseUserIds(String userIdsStr) {
        List<Long> userIds = new ArrayList<>();
        try {
            if (userIdsStr.startsWith("[") && userIdsStr.endsWith("]")) {
                // 数组格式：[1, 2, 3]
                userIdsStr = userIdsStr.substring(1, userIdsStr.length() - 1);
            }

            String[] idStrings = userIdsStr.split(",\\s*");
            for (String idStr : idStrings) {
                if (StringUtils.isNotEmpty(idStr.trim())) {
                    userIds.add(Long.parseLong(idStr.trim()));
                }
            }
        } catch (Exception e) {
            log.error("解析用户ID字符串失败: {}", userIdsStr, e);
        }
        return userIds;
    }

    /**
     * 获取用户显示名称
     */
    private String getUserDisplayName(Long userId) {
        try {
            Class<?> userClass = Class.forName("org.dromara.system.domain.SysUser");
            Object user = OperatorLogQueryUtil.getObj(String.valueOf(userId), userClass);
            if (user != null) {
                OperatorLogMetaDataBuilder builder = new OperatorLogMetaDataBuilder();
                OperatorLogMetaData metaData = builder.getChangeModel(user, userClass);
                String displayName = metaData.getDisplayName();
                if (displayName != null) {
                    return displayName;
                }
            }
        } catch (Exception e) {
            log.debug("获取用户显示名称失败: userId={}", userId, e);
        }
        return "用户ID:" + userId;
    }

    /**
     * 获取角色显示名称
     */
    private String getRoleDisplayName(Long roleId) {
        try {
            Class<?> roleClass = Class.forName("org.dromara.system.domain.SysRole");
            Object role = OperatorLogQueryUtil.getObj(String.valueOf(roleId), roleClass);
            if (role != null) {
                OperatorLogMetaDataBuilder builder = new OperatorLogMetaDataBuilder();
                OperatorLogMetaData metaData = builder.getChangeModel(role, roleClass);
                String displayName = metaData.getDisplayName();
                if (displayName != null) {
                    return displayName;
                }
            }
        } catch (Exception e) {
            log.debug("获取角色显示名称失败: roleId={}", roleId, e);
        }
        return "角色ID:" + roleId;
    }

    /**
     * 将请求对象转换为字符串
     */
    private String convertRequestObjToString(Object requestObj) {
        if (requestObj == null) {
            return "";
        }

        if (requestObj instanceof Long[]) {
            Long[] array = (Long[]) requestObj;
            return Arrays.toString(array);
        } else if (requestObj instanceof Long) {
            return requestObj.toString();
        } else if (requestObj instanceof String) {
            return (String) requestObj;
        } else {
            return requestObj.toString();
        }
    }
}
