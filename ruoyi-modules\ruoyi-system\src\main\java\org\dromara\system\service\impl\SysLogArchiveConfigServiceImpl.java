package org.dromara.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.SysLogArchiveConfig;
import org.dromara.system.domain.bo.SysLogArchiveConfigBo;
import org.dromara.system.domain.vo.SysLogArchiveConfigVo;
import org.dromara.system.mapper.SysLogArchiveConfigMapper;
import org.dromara.system.service.ISysLogArchiveConfigService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 日志转存配置Service业务层处理
 */
@RequiredArgsConstructor
@Service
public class SysLogArchiveConfigServiceImpl implements ISysLogArchiveConfigService {

    private final SysLogArchiveConfigMapper baseMapper;

    /**
     * 查询日志转存配置
     */
    @Override
    public SysLogArchiveConfigVo queryById(Long configId) {
        return baseMapper.selectVoById(configId);
    }

    /**
     * 查询日志转存配置列表
     */
    @Override
    public TableDataInfo<SysLogArchiveConfigVo> queryPageList(SysLogArchiveConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysLogArchiveConfig> lqw = buildQueryWrapper(bo);
        Page<SysLogArchiveConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    private LambdaQueryWrapper<SysLogArchiveConfig> buildQueryWrapper(SysLogArchiveConfigBo bo) {
        LambdaQueryWrapper<SysLogArchiveConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getRetainMonths() != null, SysLogArchiveConfig::getRetainMonths, bo.getRetainMonths());
        lqw.like(StringUtils.isNotBlank(bo.getArchivePath()), SysLogArchiveConfig::getArchivePath, bo.getArchivePath());
        lqw.eq(StringUtils.isNotBlank(bo.getAutoArchive()), SysLogArchiveConfig::getAutoArchive, bo.getAutoArchive());
        return lqw;
    }

    /**
     * 新增日志转存配置
     */
    @Override
    public Boolean insertByBo(SysLogArchiveConfigBo bo) {
        SysLogArchiveConfig add = MapstructUtils.convert(bo, SysLogArchiveConfig.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setConfigId(add.getConfigId());
        }
        return flag;
    }

    /**
     * 修改日志转存配置
     */
    @Override
    public Boolean updateByBo(SysLogArchiveConfigBo bo) {
        SysLogArchiveConfig update = MapstructUtils.convert(bo, SysLogArchiveConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 获取当前配置
     */
    @Override
    public SysLogArchiveConfigVo getCurrentConfig() {
        List<SysLogArchiveConfigVo> list = baseMapper.selectVoList(new LambdaQueryWrapper<>());
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysLogArchiveConfig entity) {
        // TODO 做一些数据校验,如唯一约束
    }
}
