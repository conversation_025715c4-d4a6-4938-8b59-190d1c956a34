package org.dromara.cssrcSoftware.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import org.dromara.common.encrypt.annotation.EncryptField;
import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 软件版本详情对象 software_version
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("software_version")
@Schema(description = "软件版本详情")
public class SoftwareVersion extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 版本ID
     */
    @TableId(value = "version_id")
    private Long versionId;

    /**
     * 软件ID
     */
    private Long softwareId;

    /**
     * 版本号（如 v1.0.0）
     */
    private String versionName;

    /**
     * 平台（windows/linux）
     */
    private String platform;

    /**
     * 架构（x86/x64/arm64）
     */
    private String architecture;

    /**
     * 包类型（exe/msi/deb/rpm/tar.gz）
     */
    private String packageType;

    /**
     * 位数（32/64）
     */
    private String bits;

    /**
     * 文件id
     */
    @TableField(value = "oss_id", updateStrategy = FieldStrategy.ALWAYS)
    private Long ossId;

    /**
     * 文件大小(GB)
     */
    private Double fileSize;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审批状态
     */
    private String approvalStatus;

    /**
     * 前台显示状态（0启用 1禁用）
     */
    private String displayStatus;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 下载次数
     */
    private Long downloadCount;

    /**
     * 版本密级
     */
    @EncryptField
    private String secret;
}
