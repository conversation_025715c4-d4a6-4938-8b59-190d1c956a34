package org.dromara.system.service;

import lombok.Data;

/**
 * 日志转存服务接口
 */
public interface ISysLogArchiveService {

    /**
     * 检查并执行日志转存
     */
    void checkAndArchiveLogs();

    /**
     * 手动执行日志转存
     */
    void manualArchiveLogs();

//    /**
//     * 获取日志存储统计信息
//     */
//    LogStorageInfo getLogStorageInfo();

//    /**
//     * 检查告警条件
//     */
//    void checkAlarmConditions();

    /**
     * 日志存储信息
     */
    @Data
    class LogStorageInfo {
        private Long loginLogCount;
        private Long operLogCount;
        private String storagePath;
    }
}
