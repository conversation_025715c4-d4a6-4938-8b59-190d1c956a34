package org.dromara.system.domain.bo;

import org.dromara.common.encrypt.annotation.EncryptField;
import org.dromara.system.domain.SysStandard;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 标准业务对象 sys_standard
 *
 * <AUTHOR> Li
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysStandard.class, reverseConvertGenerate = false)
public class SysStandardBo extends BaseEntity {

    /**
     * 标准id
     */
    @NotNull(message = "标准id不能为空", groups = { EditGroup.class })
    private Long standardId;

    /**
     * 标准编号
     */
    private String standardCode;

    /**
     * 标准名称
     */
    private String standardName;

    /**
     * 密级
     */
    @NotNull(message = "标准密级不能为空", groups = { AddGroup.class, EditGroup.class })
    private String secret;

    /**
     * 标准状态（0正常 1停用）
     */
    private String standardStatus;

    /**
     * 标准文件
     */
    private Long standardFile;


}
