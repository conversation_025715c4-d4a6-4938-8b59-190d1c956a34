package org.dromara.common.log.strategy;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.service.DictService;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.log.content.OperatorLogMetaDataBuilder;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.StringJoiner;

@Slf4j
@Component
public class SearchOperatorLogStrategy implements IOperateLogStrategy {

    private static final String[] EXCLUDE_SEARCH_PROPERTIES = {
        "pageNum", "pageSize", "orderByColumn", "isAsc"
    };

    @Override
    public String getOperatorContent(Object requestObj, Class<?>[] tableEntity, String operatorObjId) {
        Map<String, String> paramsMap = ServletUtils.getParamMap(ServletUtils.getRequest());
        if (ObjectUtil.isEmpty(paramsMap)) {
            return "进行了[查询]操作";
        }

        // 移除分页参数
        for (String excludeProperty : EXCLUDE_SEARCH_PROPERTIES) {
            paramsMap.remove(excludeProperty);
        }

        // 构建查询条件描述
        StringJoiner conditions = new StringJoiner(", ", "，查询条件：", "");
        DictService dictService = SpringUtils.getBean(DictService.class);

        paramsMap.forEach((key, value) -> {
            if (StringUtils.isNotEmpty(value)) {
                try {
                    // 获取字段中文名称 和 枚举值转换
                    String fieldContent = OperatorLogMetaDataBuilder.getColumnCommentAndDictValue(
                        tableEntity[0],
                        key,
                        value
                    );
                    conditions.add(fieldContent);
                } catch (Exception e) {
                    conditions.add(key + "=" + value);
                }
            }
        });

        return "进行了[查询]操作" + (conditions.length() > 0 ? conditions.toString() : "");
    }
}
