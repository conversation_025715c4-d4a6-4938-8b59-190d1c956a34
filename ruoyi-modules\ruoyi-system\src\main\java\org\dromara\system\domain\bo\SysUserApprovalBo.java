package org.dromara.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.system.domain.SysUserApproval;

/**
 * 用户审批申请业务对象 sys_user_approval
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysUserApproval.class, reverseConvertGenerate = true)
public class SysUserApprovalBo extends BaseEntity {

    /**
     * 审批ID
     */
    @NotNull(message = "审批ID不能为空", groups = { EditGroup.class })
    private Long approvalId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 审批类型（add新增 edit修改 delete删除）
     */
    @NotBlank(message = "审批类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String approvalType;

    /**
     * 审批数据（JSON格式）
     */
    private String approvalData;

    /**
     * 审批状态
     */
    private String approvalStatus;

    /**
     * 流程实例ID
     */
    private Long flowInstanceId;

    /**
     * 备注
     */
    private String remark;
}
