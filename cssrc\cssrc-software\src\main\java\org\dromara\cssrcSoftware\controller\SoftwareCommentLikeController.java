package org.dromara.cssrcSoftware.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.cssrcSoftware.domain.vo.SoftwareCommentLikeVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareCommentLikeBo;
import org.dromara.cssrcSoftware.service.ISoftwareCommentLikeService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 软件点赞关联
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/cssrc/cssrcSoftwareCommentLike")
public class SoftwareCommentLikeController extends BaseController {

    private final ISoftwareCommentLikeService softwareCommentLikeService;

    /**
     * 查询软件点赞关联列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareCommentLike:list")
    @GetMapping("/list")
    public TableDataInfo<SoftwareCommentLikeVo> list(SoftwareCommentLikeBo bo, PageQuery pageQuery) {
        return softwareCommentLikeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出软件点赞关联列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareCommentLike:export")
    @Log(title = "软件点赞关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SoftwareCommentLikeBo bo, HttpServletResponse response) {
        List<SoftwareCommentLikeVo> list = softwareCommentLikeService.queryList(bo);
        ExcelUtil.exportExcel(list, "软件点赞关联", SoftwareCommentLikeVo.class, response);
    }

    /**
     * 获取软件点赞关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("cssrc:cssrcSoftwareCommentLike:query")
    @GetMapping("/{id}")
    public R<SoftwareCommentLikeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(softwareCommentLikeService.queryById(id));
    }

    /**
     * 新增软件点赞关联
     */
    @SaCheckPermission("cssrc:cssrcSoftwareCommentLike:add")
    @Log(title = "软件点赞关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SoftwareCommentLikeBo bo) {
        return toAjax(softwareCommentLikeService.insertByBo(bo));
    }

    /**
     * 修改软件点赞关联
     */
    @SaCheckPermission("cssrc:cssrcSoftwareCommentLike:edit")
    @Log(title = "软件点赞关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SoftwareCommentLikeBo bo) {
        return toAjax(softwareCommentLikeService.updateByBo(bo));
    }

    /**
     * 删除软件点赞关联
     *
     * @param ids 主键串
     */
    @SaCheckPermission("cssrc:cssrcSoftwareCommentLike:remove")
    @Log(title = "软件点赞关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(softwareCommentLikeService.deleteWithValidByIds(List.of(ids), true));
    }

    // ==================== 用户侧接口（无权限验证） ====================

    /**
     * 用户侧点赞/取消点赞评论（无权限验证）
     */
    @Log(title = "软件点赞关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/public/toggle")
    public R<Void> publicToggleLike(@Validated(AddGroup.class) @RequestBody SoftwareCommentLikeBo bo) {
        return toAjax(softwareCommentLikeService.insertByBo(bo));
    }

    /**
     * 用户侧查询用户对评论的点赞状态（无权限验证）
     *
     * @param commentId 评论ID
     * @param userId 用户ID
     */
    @GetMapping("/public/status")
    public R<SoftwareCommentLikeVo> publicGetLikeStatus(@RequestParam Long commentId, @RequestParam Long userId) {
        SoftwareCommentLikeBo bo = new SoftwareCommentLikeBo();
        bo.setCommentId(commentId);
        bo.setUserId(userId);
        List<SoftwareCommentLikeVo> list = softwareCommentLikeService.queryList(bo);
        if (list.isEmpty()) {
            return R.ok(null);
        }
        return R.ok(list.get(0));
    }
}
