package org.dromara.system.controller.system;


import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.QueryGroup;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.system.domain.SysOss;
import org.dromara.system.domain.SysRole;
import org.dromara.system.domain.bo.SysOssBo;
import org.dromara.system.domain.bo.SysOssPartUploadBo;
import org.dromara.system.domain.vo.SysOssPartUploadVo;
import org.dromara.system.domain.vo.SysOssUploadVo;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.ISysOssService;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 文件上传 控制层
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/resource/oss")
public class SysOssController extends BaseController {

    private final ISysOssService ossService;

    /**
     * 查询OSS对象存储列表
     */
    @SaCheckPermission("system:oss:list")
    @GetMapping("/list")
    public TableDataInfo<SysOssVo> list(@Validated(QueryGroup.class) SysOssBo bo, PageQuery pageQuery) {
        return ossService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询OSS对象基于id串
     *
     * @param ossIds OSS对象ID串
     */
    @SaCheckPermission("system:oss:query")
    @GetMapping("/listByIds/{ossIds}")
    public R<List<SysOssVo>> listByIds(@NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] ossIds) {
        List<SysOssVo> list = ossService.listByIds(Arrays.asList(ossIds));
        return R.ok(list);
    }

    /**
     * 上传OSS对象存储
     *
     * @param file 文件
     * @param fileSecret 文件密级
     * @param configKey 配置key（区分不同的桶）
     */
    @SaCheckPermission("system:oss:upload")
    @Log(title = "OSS对象存储",
        businessType = BusinessType.INSERT,
        requestObjSpel = "#result", // 因为这里result对应的其实是uploadVo对象，直接可以处理
        tableEntity = SysOss.class
    )
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<SysOssUploadVo> upload(
        @RequestPart("file") MultipartFile file,
        @RequestParam("fileSecret") String fileSecret,
        @RequestParam(value = "configKey", required = false, defaultValue = "minio") String configKey
    ) {
        if (ObjectUtil.isNull(file)) {
            return R.fail("上传文件不能为空");
        }
        SysOssVo oss = ossService.upload(file, fileSecret, configKey);
        SysOssUploadVo uploadVo = new SysOssUploadVo();
        uploadVo.setUrl(oss.getUrl());
        uploadVo.setFileName(oss.getOriginalName());
        uploadVo.setOssId(oss.getOssId().toString());
        uploadVo.setFileSecret(oss.getFileSecret());
        return R.ok(uploadVo);
    }

    /**
     * 下载OSS对象
     *
     * @param ossId OSS对象ID
     */
    @SaCheckPermission("system:oss:download")
    @GetMapping("/download/{ossId}")
    @Log(title = "OSS对象存储",
        businessType = BusinessType.DOWNLOAD,
        requestObjSpel = "#ossId",
        tableEntity = SysOss.class
    )
    public void download(@PathVariable Long ossId, HttpServletResponse response) throws IOException {
        // 先获取文件信息
//        SysOssVo ossInfo = ossService.getById(ossId);

        ossService.download(ossId, response);
    }

    /**
     * 删除OSS对象存储
     *
     * @param ossIds OSS对象ID串
     */
    @SaCheckPermission("system:oss:remove")
    @Log(title = "OSS对象存储", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ossIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ossIds) {
        return toAjax(ossService.deleteWithValidByIds(List.of(ossIds), true));
    }

    /**
     * 上传OSS对象分片文件
     *
     * @param file 分片文件
     * @param bo   OSS分片上传业务对象
     * @return R 上传结果
     */
    @SaCheckPermission("system:oss:upload")
    @Log(title = "OSS对象存储-分片上传", businessType = BusinessType.INSERT)
    @PostMapping(value = "/part/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<SysOssPartUploadVo> partUpload(@RequestPart("file") MultipartFile file, @Validated SysOssPartUploadBo bo) {
        if (ObjectUtil.isNull(file)) {
            return R.fail("上传文件不能为空");
        }
        return R.ok(ossService.partUpload(file, bo));
    }
}
