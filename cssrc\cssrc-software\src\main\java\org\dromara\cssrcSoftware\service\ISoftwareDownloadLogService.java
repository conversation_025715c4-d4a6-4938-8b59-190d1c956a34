package org.dromara.cssrcSoftware.service;

import org.dromara.cssrcSoftware.domain.vo.SoftwareDownloadLogVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareDownloadLogBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 软件下载日志Service接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface ISoftwareDownloadLogService {

    /**
     * 查询软件下载日志
     *
     * @param logId 主键
     * @return 软件下载日志
     */
    SoftwareDownloadLogVo queryById(Long logId);

    /**
     * 分页查询软件下载日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 软件下载日志分页列表
     */
    TableDataInfo<SoftwareDownloadLogVo> queryPageList(SoftwareDownloadLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的软件下载日志列表
     *
     * @param bo 查询条件
     * @return 软件下载日志列表
     */
    List<SoftwareDownloadLogVo> queryList(SoftwareDownloadLogBo bo);

    /**
     * 新增软件下载日志
     *
     * @param bo 软件下载日志
     * @return 是否新增成功
     */
    Boolean insertByBo(SoftwareDownloadLogBo bo);

    /**
     * 修改软件下载日志
     *
     * @param bo 软件下载日志
     * @return 是否修改成功
     */
    Boolean updateByBo(SoftwareDownloadLogBo bo);

    /**
     * 校验并批量删除软件下载日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
