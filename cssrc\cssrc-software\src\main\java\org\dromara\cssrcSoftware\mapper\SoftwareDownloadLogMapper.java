package org.dromara.cssrcSoftware.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.dromara.cssrcSoftware.domain.SoftwareDownloadLog;
import org.dromara.cssrcSoftware.domain.vo.SoftwareDownloadLogVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 软件下载日志Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@DS("software")
public interface SoftwareDownloadLogMapper extends BaseMapperPlus<SoftwareDownloadLog, SoftwareDownloadLogVo> {

}
