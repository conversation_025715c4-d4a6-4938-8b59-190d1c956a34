package org.dromara.system.domain;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.util.Date;

/**
 * 日志转存配置对象 sys_log_archive_config
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_log_archive_config")
public class SysLogArchiveConfig extends TenantEntity {

    @TableId(value = "config_id")
    private Long configId;

    /** 保留月数 */
    private Integer retainMonths;

    /** 转存路径 */
    private String archivePath;

    /** 自动转存 */
    private String autoArchive;

    /** 上次日志转存时间 */
    private Date lastArchiveTime;
}
