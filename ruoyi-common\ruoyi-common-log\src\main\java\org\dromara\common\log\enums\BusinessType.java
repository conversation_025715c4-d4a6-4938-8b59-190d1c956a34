package org.dromara.common.log.enums;

/**
 * 业务操作类型
 *
 * <AUTHOR>
 */
public enum BusinessType {
    /**
     * 其它
     */
    OTHER,

    /**
     * 新增
     */
    INSERT,

    /**
     * 修改
     */
    UPDATE,

    /**
     * 删除
     */
    DELETE,

    /**
     * 查询
     */
    SEARCH,

    /**
     * 授权
     */
    GRANT,

    /**
     * 导出
     */
    EXPORT,

    /**
     * 导入
     */
    IMPORT,

    /**
     * 强退
     */
    FORCE,

    /**
     * 生成代码
     */
    GENCODE,

    /**
     * 清空数据
     */
    CLEAN,

    /**
     * 文件下载
     */
    DOWNLOAD,

    /**
     * 流程启动（对应startWorkFlow）
     */
    WORKFLOW_START,
    
    /**
     * 任务办理（对应completeTask）
     */
    WORKFLOW_COMPLETE,

    /**
     * 流程终止（对应terminationTask）
     */
    WORKFLOW_TERMINATE,
    
    /**
     * 流程驳回（对应backProcess）
     */
    WORKFLOW_BACK,
    
    /**
     * 任务委派（对应taskOperation）
     */
    WORKFLOW_DELEGATE,
    
    /**
     * 流程挂起（对应active接口）
     */
    WORKFLOW_SUSPEND

    
}
