package org.dromara.cssrcSoftware.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.dromara.cssrcSoftware.domain.SoftwareVersion;
import org.dromara.cssrcSoftware.domain.vo.SoftwareVersionVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 软件版本详情Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@DS("software")
public interface SoftwareVersionMapper extends BaseMapperPlus<SoftwareVersion, SoftwareVersionVo> {

}
