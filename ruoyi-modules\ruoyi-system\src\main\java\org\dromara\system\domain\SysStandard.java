package org.dromara.system.domain;

import org.dromara.common.encrypt.annotation.EncryptField;
import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 标准对象 sys_standard
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_standard")
public class SysStandard extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 标准id
     */
    @TableId(value = "standard_id")
    private Long standardId;

    /**
     * 标准编号
     */
    private String standardCode;

    /**
     * 标准名称
     */
    private String standardName;

    /**
     * 密级
     */
    @EncryptField
    private String secret;

    /**
     * 标准状态（0正常 1停用）
     */
    private String standardStatus;

    /**
     * 标准文件
     */
    private Long standardFile;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
