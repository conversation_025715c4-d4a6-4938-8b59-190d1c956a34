package org.dromara.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.event.ProcessTaskEvent;
import org.dromara.common.core.domain.event.ProcessDeleteEvent;
import org.dromara.common.core.domain.event.ProcessEvent;
import org.dromara.common.core.enums.BusinessStatusEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.service.*;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StreamUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.redis.constant.BloomFilterConstants;
import org.dromara.common.redis.utils.BloomFilterUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.*;
import org.dromara.system.domain.bo.SysNoticeBo;
import org.dromara.system.domain.vo.SysNoticeVo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.mapper.*;
import org.dromara.system.service.ISysNoticeService;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RBloomFilter;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

// 添加对流程参数常量的引入


/**
 * 公告 服务层实现
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
@Slf4j
//@Component("noticeService")
public class SysNoticeServiceImpl implements ISysNoticeService {

    private final SysNoticeMapper baseMapper;
    private final SysUserMapper userMapper;
    private final SysNoticeOssMapper noticeOssMapper;
    private final SysNoticeUserMapper noticeUserMapper;
    private final WorkflowService workflowService;


    /**
     * 根据条件分页查询通知公告列表（用户前台），含有置顶
     *
     * @param notice 通知公告信息
     * @return 通知公告集合信息
     */
    @Override
    public TableDataInfo<SysNoticeVo> selectPageNoticeList(SysNoticeBo notice, PageQuery pageQuery) {
        QueryWrapper<SysNotice> wrapper = Wrappers.query();
        wrapper
            .like(StringUtils.isNotBlank(notice.getNoticeTitle()), "notice_title", notice.getNoticeTitle())
            .eq(StringUtils.isNotBlank(notice.getNoticeType()), "notice_type", notice.getNoticeType())
            .eq(StringUtils.isNotBlank(notice.getFlowStatus()), "flow_status", notice.getFlowStatus())
            .orderByAsc("CASE WHEN is_top = 1 AND DATE_ADD(create_time, INTERVAL top_day DAY) >= NOW() THEN 0 ELSE 1 END")
            .orderByDesc("update_time");

        if (StringUtils.isNotBlank(notice.getCreateByName())) {
            SysUserVo sysUser = userMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, notice.getCreateByName()));
            wrapper.eq("create_by", ObjectUtil.isNotNull(sysUser) ? sysUser.getUserId() : null);
        }
        if (StringUtils.isNotBlank(notice.getCreateByNickName())) {
            SysUserVo sysUser = userMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getNickName, notice.getCreateByNickName()));
            wrapper.eq("create_by", ObjectUtil.isNotNull(sysUser) ? sysUser.getUserId() : null);
        }

        Page<SysNoticeVo> page = baseMapper.selectVoPage(pageQuery.build(), wrapper);
        getPageDetails(page);
        return TableDataInfo.build(page);
    }

    /**
     * 根据条件分页查询通知公告列表（管理后台，含有数据权限）
     *
     * @param notice 通知公告信息
     * @return 通知公告集合信息
     */
    @Override
    public TableDataInfo<SysNoticeVo> selectPageNoticeManagementList(SysNoticeBo notice, PageQuery pageQuery) {
        LambdaQueryWrapper<SysNotice> lqw =  buildQueryWrapper(notice);
//        QueryWrapper<SysNotice> wrapper = Wrappers.query();
//        wrapper.orderByDesc("update_time");
        Page<SysNoticeVo> page = baseMapper.selectVoPageWithPermission(pageQuery.build(), lqw);
        getPageDetails(page);
        return TableDataInfo.build(page);
    }

    /**
     * 获取分页后的详情,添加附件信息和已读未读
     *
     * @param page
     */
    private void getPageDetails(Page<SysNoticeVo> page) {
        // 获取当前用户ID
        Long userId = LoginHelper.getUserId();
        // 获取7天前的时间
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(BloomFilterConstants.NOTICE_RETAIN_DAYS);

        // 填充附件信息和已读状态
        for (SysNoticeVo vo : page.getRecords()) {
            // 1. 填充附件信息和下载次数
            List<SysNoticeOss> noticeOssList = noticeOssMapper.selectNoticeOssByNoticeId(vo.getNoticeId());
            if (!noticeOssList.isEmpty()) {
                vo.setOssIds(noticeOssList.stream().map(SysNoticeOss::getOssId).toArray(Long[]::new));
                vo.setDownloadCount(noticeOssList.stream().mapToInt(SysNoticeOss::getDownloadCount).toArray());
            } else {
                vo.setOssIds(new Long[0]);
                vo.setDownloadCount(new int[0]);
            }

            // 2. 设置已读状态
            LocalDateTime updateTime = DateUtil.toLocalDateTime(vo.getUpdateTime());
            if (updateTime.isBefore(sevenDaysAgo)) {
                // 7天前的通知默认已读
                vo.setIsRead(true);
            } else {
                String filterName = BloomFilterConstants.NOTICE_READ_KEY + vo.getNoticeId();
                // 从布隆过滤器中获取是否已读，不存在或未读都返回false
                vo.setIsRead(BloomFilterUtils.contains(filterName, userId));
            }

        }

    }

    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    @Override
    public SysNoticeVo selectNoticeById(Long noticeId) {
        SysNoticeVo notice = baseMapper.selectVoById(noticeId);
        if (ObjectUtil.isNull(notice)) {
            return notice;
        }

        List<SysNoticeOss> noticeOssList = noticeOssMapper.selectNoticeOssByNoticeId(noticeId);
        if (!noticeOssList.isEmpty()) {
            // 填充通知公告关联的附件信息
            notice.setOssIds(noticeOssList.stream().map(SysNoticeOss::getOssId).toArray(Long[]::new));
            // 填充通知公告附件下载次数
            notice.setDownloadCount(noticeOssList.stream().mapToInt(SysNoticeOss::getDownloadCount).toArray());
        } else {
            notice.setOssIds(new Long[0]);
            notice.setDownloadCount(new int[0]);
        }

        // 通知公告浏览次数加1
        baseMapper.increaseViewCount(noticeId);

        // 标记为已读
        markAsRead(noticeId);

        return notice;
    }

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    @Override
    public List<SysNoticeVo> selectNoticeList(SysNoticeBo notice) {
        LambdaQueryWrapper<SysNotice> lqw = buildQueryWrapper(notice);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 构造通知公告查询信息
     * @param bo
     * @return
     */
    private LambdaQueryWrapper<SysNotice> buildQueryWrapper(SysNoticeBo bo) {
        LambdaQueryWrapper<SysNotice> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getNoticeTitle()), SysNotice::getNoticeTitle, bo.getNoticeTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getNoticeType()), SysNotice::getNoticeType, bo.getNoticeType());
        lqw.eq(StringUtils.isNotBlank(bo.getFlowStatus()), SysNotice::getFlowStatus, bo.getFlowStatus());
        // 创建人工号模糊搜索
        if (StringUtils.isNotBlank(bo.getCreateByName())) {
            // 使用模糊查询（匹配用户名的前几个字）
            LambdaQueryWrapper<SysUser> userQuery = new LambdaQueryWrapper<SysUser>()
                .like(SysUser::getUserName, bo.getCreateByName()); // 使用 likeLeft 实现 "以输入开头" 的模糊匹配
            List<SysUserVo> sysUsers = userMapper.selectUserList(userQuery);
            List<Long> userIds = sysUsers.stream()
                .map(SysUserVo::getUserId)
                .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(userIds)) {
                lqw.in(SysNotice::getCreateBy, userIds); // 将多个用户ID作为查询条件
            }
        }
        // 创建人名称模糊搜索
        if (StringUtils.isNotBlank(bo.getCreateByNickName())) {
            LambdaQueryWrapper<SysUser> userQuery = new LambdaQueryWrapper<SysUser>()
                .like(SysUser::getNickName, bo.getCreateByNickName());
            List<SysUserVo> sysUsers = userMapper.selectUserList(userQuery);
            List<Long> userIds = sysUsers.stream()
                .map(SysUserVo::getUserId)
                .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(userIds)) {
                lqw.in(SysNotice::getCreateBy, userIds);
            }
        }
//        lqw.orderByAsc(SysNotice::getNoticeId);
        lqw.orderByDesc(SysNotice::getUpdateTime); // 修改为按更新时间倒序
        return lqw;
    }

    /**
     * 新增公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    public SysNoticeVo insertNotice(SysNoticeBo notice) {
        SysNotice sysNotice = MapstructUtils.convert(notice, SysNotice.class);
        // 添加流程状态(草稿)
        if (StringUtils.isBlank(sysNotice.getFlowStatus())) {
            sysNotice.setFlowStatus(BusinessStatusEnum.DRAFT.getStatus());
        }
        boolean flag = baseMapper.insert(sysNotice) > 0;
        if (flag) {
            notice.setNoticeId(sysNotice.getNoticeId());
            // 新增通知公告和附件关联
            insertNoticeOssIds(notice, false);

            // // 初始化布隆过滤器
            // String filterName = BloomFilterConstants.NOTICE_READ_KEY + sysNotice.getNoticeId();
            // BloomFilterUtils.create(
            //     filterName,
            //     BloomFilterConstants.DEFAULT_EXPECTED_INSERTIONS,
            //     BloomFilterConstants.DEFAULT_FALSE_PROBABILITY,
            //     BloomFilterConstants.NOTICE_RETAIN_DAYS
            // );
        }

        return MapstructUtils.convert(sysNotice, SysNoticeVo.class);
    }

    /**
     * 修改公告
     *
     * @param bo 公告信息
     * @return 结果
     */
    @Override
    public SysNoticeVo updateNotice(SysNoticeBo bo) {
        // 新增通知公告和附件关联
        insertNoticeOssIds(bo, true);

        SysNotice notice = MapstructUtils.convert(bo, SysNotice.class);
        int flag = baseMapper.updateById(notice);
        if (flag < 1) {
            throw new ServiceException("修改通知公告" + notice.getNoticeTitle() + "失败");
        }
        return MapstructUtils.convert(notice, SysNoticeVo.class);
    }

    /**
     * 删除公告对象
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    @Override
    public int deleteNoticeById(Long noticeId) {
        return baseMapper.deleteById(noticeId);
    }

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    @Override
    public int deleteNoticeByIds(List<Long> noticeIds) {
        workflowService.deleteInstance(noticeIds);
        return baseMapper.deleteByIds(noticeIds);
    }

    /**
     * 新增通知公告文件关联信息
     *
     * @param notice  用户对象
     * @param clear 清除已存在的关联数据
     */
    private void insertNoticeOssIds(SysNoticeBo notice, boolean clear) {
        Long[] ossIds = notice.getOssIds();
        if (ArrayUtil.isNotEmpty(ossIds)) {
            if (clear) {
                // 删除通知公告与文件关联
                noticeOssMapper.delete(new LambdaQueryWrapper<SysNoticeOss>().eq(SysNoticeOss::getNoticeId, notice.getNoticeId()));
            }
            // 新增通知公告与文件关联
            List<SysNoticeOss> list = StreamUtils.toList(List.of(ossIds), ossId -> {
                SysNoticeOss no = new SysNoticeOss();
                no.setNoticeId(notice.getNoticeId());
                no.setOssId(ossId);
                return no;
            });
            noticeOssMapper.insertBatch(list);
        }
    }

    public int increaseNoticeOssDownloadCount(Long noticeId, Long ossId) {
        if (noticeOssMapper.increaseDownloadCount(noticeId, ossId) >=0 ) {
            return noticeOssMapper.getDownloadCount(noticeId, ossId);
        }
        return 0;

    }

    /**
     * 删除通知公告和文件的关联关系
     * @param noticeId
     * @param ossIds
     * @return
     */
    public Boolean deleteNoticeOssRelation(Long noticeId, List<Long> ossIds) {
        return noticeOssMapper.delete(new LambdaQueryWrapper<SysNoticeOss>()
            .eq(SysNoticeOss::getNoticeId, noticeId)
            .in(SysNoticeOss::getOssId, ossIds)) > 0;
    }

    /**
     * 初始化通知公告布隆过滤器
     */
    public void initBloomFilter() {
        // 1. 获取7天内的通知公告
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(BloomFilterConstants.NOTICE_RETAIN_DAYS);
        LambdaQueryWrapper<SysNotice> lqw = new LambdaQueryWrapper<>();
        lqw.ge(SysNotice::getUpdateTime, sevenDaysAgo);
        List<SysNotice> noticeList = baseMapper.selectList(lqw);

        if (CollUtil.isEmpty(noticeList)) {
            return;
        }

        // 2. 为每个通知公告创建布隆过滤器
        for (SysNotice notice : noticeList) {
            String filterName = BloomFilterConstants.NOTICE_READ_KEY + notice.getNoticeId();
            RBloomFilter<Long> bloomFilter = BloomFilterUtils.create(
                filterName,
                BloomFilterConstants.DEFAULT_EXPECTED_INSERTIONS,
                BloomFilterConstants.DEFAULT_FALSE_PROBABILITY,
                BloomFilterConstants.NOTICE_RETAIN_DAYS
            );

            // 3. 查询该通知的已读记录
            LambdaQueryWrapper<SysNoticeUser> userLqw = new LambdaQueryWrapper<>();
            userLqw.eq(SysNoticeUser::getNoticeId, notice.getNoticeId());
            List<SysNoticeUser> users = noticeUserMapper.selectList(userLqw);

            // 4. 添加已读用户到布隆过滤器
            if (CollUtil.isNotEmpty(users)) {
                for (Long userId : users.stream().map(SysNoticeUser::getUserId).collect(Collectors.toList())) {
                    bloomFilter.add(userId);
                }
            }
        }
    }


    /**
     * 标记通知公告为已读
     *
     * @param noticeId 公告ID
     */
    @Override
    public void markAsRead(Long noticeId) {
        Long userId = LoginHelper.getUserId();

        // 1. 检查是否已读
        LambdaQueryWrapper<SysNoticeUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysNoticeUser::getNoticeId, noticeId)
                    .eq(SysNoticeUser::getUserId, userId);
        Long count = noticeUserMapper.selectCount(queryWrapper);
        if (count > 0) {
            // 已读则直接返回
            return;
        }

        // 2. 检查并初始化布隆过滤器
        String filterName = BloomFilterConstants.NOTICE_READ_KEY + noticeId;
        if (!RedisUtils.hasKey(filterName)) {
            BloomFilterUtils.create(
                filterName,
                BloomFilterConstants.DEFAULT_EXPECTED_INSERTIONS,
                BloomFilterConstants.DEFAULT_FALSE_PROBABILITY,
                BloomFilterConstants.NOTICE_RETAIN_DAYS
            );
        }

        // 3. 添加到布隆过滤器
        BloomFilterUtils.add(filterName, userId);

        // 4. 添加到数据库
        SysNoticeUser noticeUser = new SysNoticeUser();
        noticeUser.setNoticeId(noticeId);
        noticeUser.setUserId(userId);
        noticeUser.setReadTime(new Date());
        noticeUserMapper.insert(noticeUser);
    }

    /**
     * 总体流程监听(例如: 草稿，撤销，退回，作废，终止，已完成，单任务完成等)
     * 正常使用只需#processEvent.flowCode=='notice'
     * @param processEvent 参数
     */
    @EventListener(condition = "#processEvent.flowCode.equals('notice')")
    public void processHandler(ProcessEvent processEvent) {
        log.info("当前任务执行了{}", processEvent.toString());
        SysNotice sysNotice = baseMapper.selectById(Convert.toLong(processEvent.getBusinessId()));
        sysNotice.setFlowStatus(processEvent.getStatus());
        // 用于例如审批附件 审批意见等 存储到业务表内 自行根据业务实现存储流程
        Map<String, Object> params = processEvent.getParams();
        if (MapUtil.isNotEmpty(params)) {
            // 历史任务扩展(通常为附件)
            String hisTaskExt = Convert.toStr(params.get("hisTaskExt"));
            // 办理人
            String handler = Convert.toStr(params.get("handler"));
            // 办理意见
            String message = Convert.toStr(params.get("message"));
        }

        // 从流程当前流转信息更新业务中对应的流程状态
        sysNotice.setFlowStatus(processEvent.getStatus());

        baseMapper.updateById(sysNotice);
    }

    /**
     * 执行任务创建监听
     * 示例：也可通过  @EventListener(condition = "#processTaskEvent.flowCode=='leave1'")进行判断
     * 在方法中判断流程节点key
     * if ("xxx".equals(processTaskEvent.getNodeCode())) {
     * //执行业务逻辑
     * }
     *
     * @param processTaskEvent 参数
     */
    @EventListener(condition = "#processTaskEvent.flowCode.equals('notice')")
    public void processTaskHandler(ProcessTaskEvent processTaskEvent) {
        log.info("当前任务执行了{}", processTaskEvent.toString());

//        // 更新业务表中流程状态
//        SysNotice sysNotice = baseMapper.selectById(Convert.toLong(processTaskEvent.getBusinessId()));
//        sysNotice.setFlowStatus(BusinessStatusEnum.WAITING.getStatus());
//        baseMapper.updateById(sysNotice);
    }

    /**
     * 监听删除流程事件
     * 正常使用只需#processDeleteEvent.flowCode=='leave1'
     * 示例为了方便则使用startsWith匹配了全部示例key
     *
     * @param processDeleteEvent 参数
     */
    @EventListener(condition = "#processDeleteEvent.flowCode.equals('notice')")
    public void processDeleteHandler(ProcessDeleteEvent processDeleteEvent) {
        log.info("监听删除流程事件，当前任务执行了{}", processDeleteEvent.toString());
        SysNotice sysNotice = baseMapper.selectById(Convert.toLong(processDeleteEvent.getBusinessId()));
        if (ObjectUtil.isNull(sysNotice)) {
            return;
        }
        baseMapper.deleteById(sysNotice.getNoticeId());
    }

}
