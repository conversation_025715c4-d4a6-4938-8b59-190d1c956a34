package org.dromara.cssrcSoftware.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 软件权限白名单对象 software_whiteuser
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("software_whiteuser")
@Schema(description = "软件授权白名单")
public class SoftwareWhiteuser extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 软件ID
     */
    private Long softwareId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 授权原因
     */
    private String grantReason;

    /**
     * 授权时间
     */
    private Date grantTime;

    /**
     * 过期时间（可选）
     */
    private Date expireTime;


}
