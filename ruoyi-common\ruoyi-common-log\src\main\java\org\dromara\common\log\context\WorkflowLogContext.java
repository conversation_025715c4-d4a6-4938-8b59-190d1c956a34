package org.dromara.common.log.context;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class WorkflowLogContext {
    // 存储流程相关的上下文信息
    private static final ThreadLocal<Map<String, Object>> CONTEXT = new ThreadLocal<>();

    public static void setContext(String processName, String nodeName, String operator, String message, String businessId, String flowCode) {
        Map<String, Object> ctx = new HashMap<>();
        ctx.put("processName", processName);
        ctx.put("nodeName", nodeName);
        ctx.put("operator", operator);
        ctx.put("message", message);
        ctx.put("businessId", businessId);
        ctx.put("flowCode", flowCode);
        CONTEXT.set(ctx);
    }

    public static Map<String, Object> getContext() {
        return CONTEXT.get();
    }

    public static void clear() {
        CONTEXT.remove();
    }
}

