package org.dromara.common.log.annotation;

import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.log.enums.OperatorType;

import java.lang.annotation.*;

/**
 * 自定义操作日志记录注解
 *
 * <AUTHOR>
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {
    /**
     * 模块
     */
    String title() default "";

    /**
     * 功能
     */
    BusinessType businessType() default BusinessType.OTHER;

    /**
     * 操作人类别
     */
    OperatorType operatorType() default OperatorType.MANAGE;

    /**
     * 是否保存请求的参数
     */
    boolean isSaveRequestData() default true;

    /**
     * 是否保存响应的参数
     */
    boolean isSaveResponseData() default true;


    /**
     * 排除指定的请求参数
     */
    String[] excludeParamNames() default {};

    /**
     * 详情
     */
    String detail() default "";

    /**
     * 用于获取请求对象,包含修改前后的值
     */
    String requestObjSpel() default "";

    /**
     * 用于获取操作对象的唯一标识
     */
    String operatorObjIdSpel() default "";

    /**
     * 用于关联数据库实体类,方便获取表结构信息
     */
    Class<?> tableEntity() default void.class;

}
