<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SysUserApprovalMapper">

    <resultMap type="org.dromara.system.domain.SysUserApproval" id="SysUserApprovalResult">
        <result property="approvalId"       column="approval_id"        />
        <result property="tenantId"         column="tenant_id"          />
        <result property="userId"           column="user_id"            />
        <result property="approvalType"     column="approval_type"      />
        <result property="approvalData"     column="approval_data"      />
        <result property="approvalStatus"   column="approval_status"    />
        <result property="flowInstanceId"   column="flow_instance_id"   />
        <result property="delFlag"          column="del_flag"           />
        <result property="createDept"       column="create_dept"        />
        <result property="createBy"         column="create_by"          />
        <result property="createTime"       column="create_time"        />
        <result property="updateBy"         column="update_by"          />
        <result property="updateTime"       column="update_time"        />
        <result property="remark"           column="remark"             />
    </resultMap>

</mapper>
