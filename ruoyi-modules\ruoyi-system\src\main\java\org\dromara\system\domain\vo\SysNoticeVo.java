package org.dromara.system.domain.vo;

import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.encrypt.annotation.EncryptField;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.system.domain.SysNotice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 通知公告视图对象 sys_notice
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysNotice.class)
public class SysNoticeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 公告ID
     */
    private Long noticeId;

    /**
     * 公告标题
     */
    private String noticeTitle;

    /**
     * 公告类型（1通知 2公告）
     */
    private String noticeType;

    /**
     * 公告来源
     */
    private String noticeSource;

    /**
     * 公告内容
     */
    private String noticeContent;

    /**
     * 公告状态（0正常 1关闭）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建人工号
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;

    /**
     * 创建人名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "createBy")
    private String createByNickName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 通知公告文件附件ossIds
     * 关联附件
     */
    private Long[] ossIds;

    /**
     * 通知公告附件下载次数
     */
    private int[] downloadCount;

    /**
     * 置顶标志（0正常 1置顶）
     */
    private String isTop;

    /**
     * 置顶天数
     */
    private int topDay;

    /**
     * 浏览次数
     */
    private int viewCount;

    /**
     * 是否已读
     */
    private Boolean isRead;

    /**
     * 状态
     */
    private String flowStatus;

    /**
     * 公告密级
     */
    @ExcelProperty(value = "公告密级", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_file_secret")
    @EncryptField
    private String secret;

    /**
     * 公告图片
     */
    private String imgOssIds;
}
