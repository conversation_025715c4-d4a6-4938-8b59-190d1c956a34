package org.dromara.system.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.bo.SysOperLogBo;
import org.dromara.system.domain.vo.SysOperLogVo;

import java.util.Collection;
import java.util.List;

/**
 * 操作日志 服务层
 *
 * <AUTHOR> Li
 */
public interface ISysOperLogService {

    TableDataInfo<SysOperLogVo> selectPageOperLogList(SysOperLogBo operLog, PageQuery pageQuery);

    /**
     * 新增操作日志
     *
     * @param bo 操作日志对象
     */
    void insertOperlog(SysOperLogBo bo);

    /**
     * 查询系统操作日志集合
     *
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    List<SysOperLogVo> selectOperLogList(SysOperLogBo operLog);

    /**
     * 批量删除系统操作日志
     *
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    int deleteOperLogByIds(Long[] operIds);

    /**
     * 查询操作日志详细
     *
     * @param operId 操作ID
     * @return 操作日志对象
     */
    SysOperLogVo selectOperLogById(Long operId);

    /**
     * 清空操作日志
     */
    void cleanOperLog();

    /**
     * 查询操作日志记录
     *
     * @param operId 主键
     * @return 操作日志记录
     */
    SysOperLogVo queryById(Long operId);

    /**
     * 分页查询操作日志记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 操作日志记录分页列表
     */
    TableDataInfo<SysOperLogVo> queryPageList(SysOperLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的操作日志记录列表
     *
     * @param bo 查询条件
     * @return 操作日志记录列表
     */
    List<SysOperLogVo> queryList(SysOperLogBo bo);

    /**
     * 新增操作日志记录
     *
     * @param bo 操作日志记录
     * @return 是否新增成功
     */
    Boolean insertByBo(SysOperLogBo bo);

    /**
     * 修改操作日志记录
     *
     * @param bo 操作日志记录
     * @return 是否修改成功
     */
    Boolean updateByBo(SysOperLogBo bo);

    /**
     * 校验并批量删除操作日志记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
