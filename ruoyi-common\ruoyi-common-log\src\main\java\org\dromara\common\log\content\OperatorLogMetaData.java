package org.dromara.common.log.content;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 操作日志内容元模型
 * <AUTHOR> <PERSON><PERSON> hao
 */
@Getter
@Setter
public class OperatorLogMetaData {

    private String id;

    // 对象名
    private String name;

    // 对象显示名称（用于日志显示，如用户昵称、角色名称等）
    private String displayName;

    // 除复杂列表和独立子对象外的所有字段   key:字段名 value:字段值
    private Map<String,String> fieldMap = new HashMap<>();

    // 引用子对象     key:字段名 value:子对象
    private Map<String, OperatorLogMetaData> subObjectMap = new HashMap<>();

    //复杂列表字段   key:字段名 value:复杂列表
    private Map<String,List<OperatorLogMetaData>> listMap = new HashMap<>();
}
