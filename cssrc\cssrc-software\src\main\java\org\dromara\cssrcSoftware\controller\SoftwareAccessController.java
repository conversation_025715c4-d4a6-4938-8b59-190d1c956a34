package org.dromara.cssrcSoftware.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.cssrcSoftware.domain.vo.SoftwareAccessVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareAccessBo;
import org.dromara.cssrcSoftware.service.ISoftwareAccessService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 软件入网申请
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/cssrc/cssrcSoftwareAccess")
public class SoftwareAccessController extends BaseController {

    private final ISoftwareAccessService softwareAccessService;

    /**
     * 查询软件入网申请列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareAccess:list")
    @GetMapping("/list")
    public TableDataInfo<SoftwareAccessVo> list(SoftwareAccessBo bo, PageQuery pageQuery) {
        return softwareAccessService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出软件入网申请列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareAccess:export")
    @Log(title = "软件入网申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SoftwareAccessBo bo, HttpServletResponse response) {
        List<SoftwareAccessVo> list = softwareAccessService.queryList(bo);
        ExcelUtil.exportExcel(list, "软件入网申请", SoftwareAccessVo.class, response);
    }

    /**
     * 获取软件入网申请详细信息
     *
     * @param accessId 主键
     */
    @SaCheckPermission("cssrc:cssrcSoftwareAccess:query")
    @GetMapping("/{accessId}")
    public R<SoftwareAccessVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long accessId) {
        return R.ok(softwareAccessService.queryById(accessId));
    }

    /**
     * 新增软件入网申请
     */
    @SaCheckPermission("cssrc:cssrcSoftwareAccess:add")
    @Log(title = "软件入网申请", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SoftwareAccessBo bo) {
        return toAjax(softwareAccessService.insertByBo(bo));
    }

    /**
     * 修改软件入网申请
     */
    @SaCheckPermission("cssrc:cssrcSoftwareAccess:edit")
    @Log(title = "软件入网申请", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SoftwareAccessBo bo) {
        return toAjax(softwareAccessService.updateByBo(bo));
    }

    /**
     * 删除软件入网申请
     *
     * @param accessIds 主键串
     */
    @SaCheckPermission("cssrc:cssrcSoftwareAccess:remove")
    @Log(title = "软件入网申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{accessIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] accessIds) {
        return toAjax(softwareAccessService.deleteWithValidByIds(List.of(accessIds), true));
    }
}
