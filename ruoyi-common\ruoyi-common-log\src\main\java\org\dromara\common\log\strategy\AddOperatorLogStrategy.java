package org.dromara.common.log.strategy;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.log.content.ObjectAddLogUtil;
import org.dromara.common.log.utils.OperatorLogUtil;
import org.springframework.stereotype.Component;

/**
 * 新增生成变更内容模板
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AddOperatorLogStrategy implements IOperateLogStrategy {
    @Override
    public String getOperatorContent(Object requestObj, Class<?>[] tableEntity, String operatorObjId) {
        try {
            String objectType = OperatorLogUtil.getTableComment(tableEntity[0]);
            ObjectAddLogUtil objectAddLogUtil = new ObjectAddLogUtil();

            // 使用 ObjectAddLogUtil 生成详细的新增日志内容
            return objectAddLogUtil.generatorContent(
                requestObj,
                objectType,
                tableEntity[0]
            );
        } catch (Exception e) {
            log.error("生成新增对象操作日志失败", e);
            return "生成新增日志失败:" + e.getMessage();
        }
    }
}



