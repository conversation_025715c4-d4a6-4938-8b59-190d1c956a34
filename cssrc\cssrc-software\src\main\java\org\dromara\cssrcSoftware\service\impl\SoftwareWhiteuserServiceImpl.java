package org.dromara.cssrcSoftware.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.cssrcSoftware.domain.bo.SoftwareWhiteuserBo;
import org.dromara.cssrcSoftware.domain.vo.SoftwareWhiteuserVo;
import org.dromara.cssrcSoftware.domain.SoftwareWhiteuser;
import org.dromara.cssrcSoftware.mapper.SoftwareWhiteuserMapper;
import org.dromara.cssrcSoftware.service.ISoftwareWhiteuserService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 软件权限白名单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RequiredArgsConstructor
@Service
public class SoftwareWhiteuserServiceImpl implements ISoftwareWhiteuserService {

    private final SoftwareWhiteuserMapper baseMapper;

    /**
     * 查询软件权限白名单
     *
     * @param id 主键
     * @return 软件权限白名单
     */
    @Override
    public SoftwareWhiteuserVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询软件权限白名单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 软件权限白名单分页列表
     */
    @Override
    public TableDataInfo<SoftwareWhiteuserVo> queryPageList(SoftwareWhiteuserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SoftwareWhiteuser> lqw = buildQueryWrapper(bo);
        Page<SoftwareWhiteuserVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的软件权限白名单列表
     *
     * @param bo 查询条件
     * @return 软件权限白名单列表
     */
    @Override
    public List<SoftwareWhiteuserVo> queryList(SoftwareWhiteuserBo bo) {
        LambdaQueryWrapper<SoftwareWhiteuser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SoftwareWhiteuser> buildQueryWrapper(SoftwareWhiteuserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SoftwareWhiteuser> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SoftwareWhiteuser::getId);
        lqw.eq(bo.getSoftwareId() != null, SoftwareWhiteuser::getSoftwareId, bo.getSoftwareId());
        lqw.eq(bo.getUserId() != null, SoftwareWhiteuser::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getGrantReason()), SoftwareWhiteuser::getGrantReason, bo.getGrantReason());
        lqw.eq(bo.getGrantTime() != null, SoftwareWhiteuser::getGrantTime, bo.getGrantTime());
        lqw.eq(bo.getExpireTime() != null, SoftwareWhiteuser::getExpireTime, bo.getExpireTime());
        return lqw;
    }

    /**
     * 新增软件权限白名单
     *
     * @param bo 软件权限白名单
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SoftwareWhiteuserBo bo) {
        SoftwareWhiteuser add = MapstructUtils.convert(bo, SoftwareWhiteuser.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改软件权限白名单
     *
     * @param bo 软件权限白名单
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SoftwareWhiteuserBo bo) {
        SoftwareWhiteuser update = MapstructUtils.convert(bo, SoftwareWhiteuser.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SoftwareWhiteuser entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除软件权限白名单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
