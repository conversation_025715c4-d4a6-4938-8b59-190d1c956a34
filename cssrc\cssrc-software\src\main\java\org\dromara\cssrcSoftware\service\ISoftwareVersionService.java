package org.dromara.cssrcSoftware.service;

import org.dromara.cssrcSoftware.domain.vo.SoftwareVersionVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareVersionBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 软件版本详情Service接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface ISoftwareVersionService {

    /**
     * 查询软件版本详情
     *
     * @param versionId 主键
     * @return 软件版本详情
     */
    SoftwareVersionVo queryById(Long versionId);

    /**
     * 分页查询软件版本详情列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 软件版本详情分页列表
     */
    TableDataInfo<SoftwareVersionVo> queryPageList(SoftwareVersionBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的软件版本详情列表
     *
     * @param bo 查询条件
     * @return 软件版本详情列表
     */
    List<SoftwareVersionVo> queryList(SoftwareVersionBo bo);

    /**
     * 新增软件版本详情
     *
     * @param bo 软件版本详情
     * @return 是否新增成功
     */
    SoftwareVersionVo insertByBo(SoftwareVersionBo bo);

    /**
     * 修改软件版本详情
     *
     * @param bo 软件版本详情
     * @return 是否修改成功
     */
    SoftwareVersionVo updateByBo(SoftwareVersionBo bo);

    /**
     * 校验并批量删除软件版本详情信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
