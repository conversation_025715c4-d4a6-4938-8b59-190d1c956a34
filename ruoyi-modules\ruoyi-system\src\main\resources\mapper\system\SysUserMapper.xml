<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SysUserMapper">

    <resultMap type="org.dromara.system.domain.vo.SysUserVo" id="SysUserResult">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
    </resultMap>
    <resultMap type="org.dromara.system.domain.vo.SysUserExportVo" id="SysUserExportResult">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="status" column="status"/>
        <result property="secret" column="secret"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="deptName" column="dept_name"/>
        <result property="leaderName" column="leaderName"/>
    </resultMap>

    <select id="selectUserExportList" resultMap="SysUserExportResult">
        select u.user_id, u.dept_id, u.nick_name, u.user_name, u.secret, u.email, u.avatar, u.phonenumber, u.sex,
            u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
            d.dept_name, d.leader, u1.user_name as leaderName
        from sys_user u
            left join sys_dept d on u.dept_id = d.dept_id
            left join sys_user u1 on u1.user_id = d.leader
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectAllocatedList" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
             left join sys_dept d on u.dept_id = d.dept_id
             left join sys_user_role sur on u.user_id = sur.user_id
             left join sys_role r on r.role_id = sur.role_id
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectUnallocatedList" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
             left join sys_dept d on u.dept_id = d.dept_id
             left join sys_user_role sur on u.user_id = sur.user_id
             left join sys_role r on r.role_id = sur.role_id
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectUserByIdIncludeDeleted" resultMap="SysUserResult">
        SELECT user_id, dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, remark, secret, flow_status, tenant_id, create_dept, create_by, create_time, update_by, update_time
        FROM sys_user
        WHERE user_id = #{userId}
    </select>

    <update id="updateUserForApproval">
        UPDATE sys_user
        SET del_flag = #{delFlag},
            update_time = NOW()
        WHERE user_id = #{userId}
    </update>

</mapper>
