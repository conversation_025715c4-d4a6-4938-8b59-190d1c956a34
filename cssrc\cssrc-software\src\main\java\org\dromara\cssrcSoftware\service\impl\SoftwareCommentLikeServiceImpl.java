package org.dromara.cssrcSoftware.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.cssrcSoftware.domain.bo.SoftwareCommentLikeBo;
import org.dromara.cssrcSoftware.domain.vo.SoftwareCommentLikeVo;
import org.dromara.cssrcSoftware.domain.SoftwareCommentLike;
import org.dromara.cssrcSoftware.mapper.SoftwareCommentLikeMapper;
import org.dromara.cssrcSoftware.service.ISoftwareCommentLikeService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 软件点赞关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RequiredArgsConstructor
@Service
public class SoftwareCommentLikeServiceImpl implements ISoftwareCommentLikeService {

    private final SoftwareCommentLikeMapper baseMapper;

    /**
     * 查询软件点赞关联
     *
     * @param id 主键
     * @return 软件点赞关联
     */
    @Override
    public SoftwareCommentLikeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询软件点赞关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 软件点赞关联分页列表
     */
    @Override
    public TableDataInfo<SoftwareCommentLikeVo> queryPageList(SoftwareCommentLikeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SoftwareCommentLike> lqw = buildQueryWrapper(bo);
        Page<SoftwareCommentLikeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的软件点赞关联列表
     *
     * @param bo 查询条件
     * @return 软件点赞关联列表
     */
    @Override
    public List<SoftwareCommentLikeVo> queryList(SoftwareCommentLikeBo bo) {
        LambdaQueryWrapper<SoftwareCommentLike> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SoftwareCommentLike> buildQueryWrapper(SoftwareCommentLikeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SoftwareCommentLike> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SoftwareCommentLike::getId);
        lqw.eq(bo.getCommentId() != null, SoftwareCommentLike::getCommentId, bo.getCommentId());
        lqw.eq(bo.getUserId() != null, SoftwareCommentLike::getUserId, bo.getUserId());
        lqw.eq(bo.getLikeType() != null, SoftwareCommentLike::getLikeType, bo.getLikeType());
        return lqw;
    }

    /**
     * 新增软件点赞关联
     *
     * @param bo 软件点赞关联
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SoftwareCommentLikeBo bo) {
        SoftwareCommentLike add = MapstructUtils.convert(bo, SoftwareCommentLike.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改软件点赞关联
     *
     * @param bo 软件点赞关联
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SoftwareCommentLikeBo bo) {
        SoftwareCommentLike update = MapstructUtils.convert(bo, SoftwareCommentLike.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SoftwareCommentLike entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除软件点赞关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
