package org.dromara.cssrcSoftware.domain.bo;

import org.dromara.cssrcSoftware.domain.SoftwareVersion;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 软件版本详情业务对象 software_version
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SoftwareVersion.class, reverseConvertGenerate = false)
public class SoftwareVersionBo extends BaseEntity {

    /**
     * 版本ID
     */
    @NotNull(message = "版本ID不能为空", groups = { EditGroup.class })
    private Long versionId;

    /**
     * 软件ID
     */
    @NotNull(message = "软件ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long softwareId;

    /**
     * 版本号（如 v1.0.0）
     */
    @NotBlank(message = "版本号（如 v1.0.0）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String versionName;

    /**
     * 平台（windows/linux）
     */
    @NotBlank(message = "平台（windows/linux）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platform;

    /**
     * 架构（x86/x64/arm64）
     */
    private String architecture;

    /**
     * 包类型（exe/msi/deb/rpm/tar.gz）
     */
    private String packageType;

    /**
     * 位数（32/64）
     */
    private String bits;

    /**
     * 文件id
     */
//    @NotBlank(message = "文件id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ossId;

    /**
     * 文件大小(GB)
     */
    private Double fileSize;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审批状态
     */
    private String approvalStatus;

    /**
     * 前台显示状态（0启用 1禁用）
     */
    private String displayStatus;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 下载次数
     */
    private Long downloadCount;

    /**
     * 版本密级
     */
    @NotNull(message = "版本密级不能为空", groups = { AddGroup.class, EditGroup.class })
    private String secret;
}
