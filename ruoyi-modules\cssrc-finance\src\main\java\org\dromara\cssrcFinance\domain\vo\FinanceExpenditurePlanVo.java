package org.dromara.cssrcFinance.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.cssrcFinance.domain.FinanceExpenditurePlan;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 财务-预算-支出计划视图对象 finance_expenditure_plan
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinanceExpenditurePlan.class)
public class FinanceExpenditurePlanVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
//    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 部门id
     */
//    @ExcelProperty(value = "部门id")
    private Long deptId;

    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    @Translation(type = TransConstant.DEPT_ID_TO_NAME, mapper = "deptId")
    private String deptName;

    /**
     * 用户id
     */
//    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户工号
     */
    @ExcelProperty(value = "工号")
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "userId")
    private String userName;

    /**
     * 用户姓名
     */
    @ExcelProperty(value = "姓名")
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "userId")
    private String nickName;

    /**
     * 支出类别（材料费、专用费、外协费、分拨款）
     */
    @ExcelProperty(value = "支出类别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "材料费=材料费,专用费=专用费,外协费=外协费,分拨款=分拨款")
    private String expenditureType;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    private String content;

    /**
     * 课题编号
     */
    @ExcelProperty(value = "课题编号")
    private String subjectNumber;

    /**
     * 课题名称
     */
    @ExcelProperty(value = "课题名称")
    private String subjectName;

    /**
     * 项目类别
     */
    @ExcelProperty(value = "项目类别")
    private String projectType;

    /**
     * 合同总金额（万元）
     */
    @ExcelProperty(value = "合同总金额")
    private Long contractTotalAmount;

    /**
     * 本次付款金额（万元）
     */
    @ExcelProperty(value = "本次付款金额")
    private Long currentPaymentAmount;

    /**
     * 付款时间
     */
    @ExcelProperty(value = "付款时间")
    private Date paymentTime;

    /**
     * 是否零余额项目（Y：是，N：否）
     */
    @ExcelProperty(value = "是否零余额项目", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String isNobalanceProject;

    /**
     * 是否重点项目（Y：是，N：否）
     */
    @ExcelProperty(value = "是否重点项目", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String isMajorProject;

    /**
     * 项目是否到款（Y：是，N：否）
     */
    @ExcelProperty(value = "项目是否到款", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String isFunded;

    /**
     * 支付方式（银行转账、银行承兑）
     */
    @ExcelProperty(value = "支付方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "银行转账=银行转账,银行承兑=银行承兑")
    private String paymentMethod;


    /**
     * 流程状态
     */
//    @ExcelProperty(value = "流程状态")
    private String flowStatus;

}
