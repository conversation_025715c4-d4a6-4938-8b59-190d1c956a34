package org.dromara.cssrcSoftware.domain.vo;

import org.dromara.common.encrypt.annotation.EncryptField;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.cssrcSoftware.domain.SoftwareInfo;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 软件基本信息视图对象 software_info
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SoftwareInfo.class)
public class SoftwareInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 软件ID
     */
    @ExcelProperty(value = "软件ID")
    private Long softwareId;

    /**
     * 软件分类ID
     */
    private Long categoryId;

    /**
     * 软件分类名称
     */
    @ExcelProperty(value = "软件分类名称")
    private String categoryName;

    /**
     * 软件名称
     */
    @ExcelProperty(value = "软件名称")
    private String softwareName;

    /**
     * 生产厂商
     */
    @ExcelProperty(value = "生产厂商")
    private String manufacturer;

    /**
     * 生产国别
     */
    @ExcelProperty(value = "生产国别")
    private String country;

    /**
     * 软件简介
     */
    @ExcelProperty(value = "软件简介")
    private String intro;

    /**
     * 状态（0正常 1停用）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 下载次数
     */
    @ExcelProperty(value = "下载次数")
    private Long downloadCount;

    /**
     * 软件密级
     */
    @EncryptField
    @ExcelProperty(value = "软件密级", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_file_secret")
    private String secret;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建人工号
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;

    /**
     * 创建人名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "createBy")
    private String createByNickName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建部门名称
     */
    @Translation(type = TransConstant.DEPT_ID_TO_NAME, mapper = "createDept")
    private String createDeptName;

    /**
     * 更新时间
     */
    private Date updateTime;
}
