package org.dromara.common.core.domain.model;


import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

@Data
@EqualsAndHashCode(callSuper = true)
public class CertLoginBody  extends LoginBody {

    /**
     * 用户名
     */
//    @NotBlank(message = "{user.username.not.blank}")
//    @Length(min = 2, max = 20, message = "{user.username.length.valid}")
    private String username;


}



