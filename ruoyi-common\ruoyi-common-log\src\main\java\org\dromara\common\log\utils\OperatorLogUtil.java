package org.dromara.common.log.utils;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Author: cheng<PERSON>.lin
 * @Date: 2023/09/21
 * @Description: 操作日志工具类
 */
public class OperatorLogUtil {
    /**
     * 通过表上的@Schema注解获取中文名，没有就用TableName
     * @param tableEntity
     * @return
     */
    public static String getTableComment(Class<?> tableEntity) {
        Schema schema = tableEntity.getDeclaredAnnotation(Schema.class);
        TableName tableNameAnnotation = tableEntity.getDeclaredAnnotation(TableName.class);
        if (schema != null) {
            return schema.description();
        }

        // 如果没schema注解就用tableName
        if (tableNameAnnotation == null) {
            throw new RuntimeException("该类[" + tableEntity.getName() + "]" + "没有标注TableName注解");
        }
        return tableNameAnnotation.value();
    }


}
