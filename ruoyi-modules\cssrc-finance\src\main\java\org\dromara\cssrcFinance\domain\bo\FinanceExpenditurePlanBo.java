package org.dromara.cssrcFinance.domain.bo;

import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.cssrcFinance.domain.FinanceExpenditurePlan;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 财务-预算-支出计划业务对象 finance_expenditure_plan
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinanceExpenditurePlan.class, reverseConvertGenerate = false)
public class FinanceExpenditurePlanBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户工号
     */
    private String userName;

    /**
     * 用户姓名
     */
    private String nickName;

    /**
     * 支出类别（材料费、专用费、外协费、分拨款）
     */
    @NotBlank(message = "支出类别（材料费、专用费、外协费、分拨款）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String expenditureType;

    /**
     * 内容
     */
    private String content;

    /**
     * 课题编号
     */
    @NotBlank(message = "课题编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String subjectNumber;

    /**
     * 课题名称
     */
    @NotBlank(message = "课题名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String subjectName;

    /**
     * 项目类别
     */
    @NotBlank(message = "项目类别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectType;

    /**
     * 合同总金额（万元）
     */
    @NotNull(message = "合同总金额（万元）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long contractTotalAmount;

    /**
     * 本次付款金额（万元）
     */
    @NotNull(message = "本次付款金额（万元）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long currentPaymentAmount;

    /**
     * 付款时间
     */
    private Date paymentTime;

    /**
     * 是否零余额项目（Y表示是，N表示否）
     */
    private String isNobalanceProject;

    /**
     * 是否重点项目（Y表示是，N表示否）
     */
    private String isMajorProject;

    /**
     * 项目是否到款（Y表示是，N表示否）
     */
    private String isFunded;

    /**
     * 支付方式（银行转账、银行承兑）
     */
    private String paymentMethod;

    /**
     * 流程状态
     */
    private String flowStatus;

    /**
     * 排序列
     */
    private String orderColumn;

    /**
     * 排序顺序
     */
    private String orderType;

}
