package org.dromara.cssrcSoftware.domain.bo;

import org.dromara.cssrcSoftware.domain.SoftwareComment;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 软件评论业务对象 software_comment
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SoftwareComment.class, reverseConvertGenerate = false)
public class SoftwareCommentBo extends BaseEntity {

    /**
     * 评论ID
     */
    @NotNull(message = "评论ID不能为空", groups = { EditGroup.class })
    private Long commentId;

    /**
     * 版本ID
     */
    @NotNull(message = "版本ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long versionId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 评论内容
     */
    @NotBlank(message = "评论内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String content;

    /**
     * 回复哪条评论
     */
    private Long replyTo;

    /**
     * 状态（0正常 1屏蔽）
     */
    private String status;


}
