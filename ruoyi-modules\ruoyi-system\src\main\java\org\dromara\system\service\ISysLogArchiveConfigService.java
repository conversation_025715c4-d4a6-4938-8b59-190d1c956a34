package org.dromara.system.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.SysLogArchiveConfig;
import org.dromara.system.domain.bo.SysLogArchiveConfigBo;
import org.dromara.system.domain.vo.SysLogArchiveConfigVo;

/**
 * 日志转存配置服务接口
 */
public interface ISysLogArchiveConfigService {

    /**
     * 查询日志转存配置
     */
    SysLogArchiveConfigVo queryById(Long configId);

    /**
     * 查询日志转存配置列表
     */
    TableDataInfo<SysLogArchiveConfigVo> queryPageList(SysLogArchiveConfigBo bo, PageQuery pageQuery);

    /**
     * 新增日志转存配置
     */
    Boolean insertByBo(SysLogArchiveConfigBo bo);

    /**
     * 修改日志转存配置
     */
    Boolean updateByBo(SysLogArchiveConfigBo bo);

    /**
     * 获取当前配置
     */
    SysLogArchiveConfigVo getCurrentConfig();
}
