package org.dromara.common.log.strategy;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.log.utils.OperatorLogContext;
import org.dromara.common.log.utils.OperatorLogUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 导入操作日志生成策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ImportOperatorLogStrategy implements IOperateLogStrategy {

    @Override
    public String getOperatorContent(Object requestObj, Class<?>[] tableEntity, String operatorObjId) {
        try {
            String objectType = OperatorLogUtil.getTableComment(tableEntity[0]);

            // 获取导入文件信息
            String fileInfo = getImportFileInfo();

            // 获取导入参数信息
            String importParams = getImportParams();

            StringBuilder content = new StringBuilder();
            content.append("导入").append(objectType).append("数据");

            if (StringUtils.isNotEmpty(fileInfo)) {
                content.append("，").append(fileInfo);
            }

            if (StringUtils.isNotEmpty(importParams)) {
                content.append("，").append(importParams);
            }

            return content.toString();
        } catch (Exception e) {
            log.error("生成导入操作日志失败", e);
            return "执行了[导入]操作";
        } finally {
            OperatorLogContext.clear();
        }
    }

    /**
     * 获取导入文件信息
     */
    private String getImportFileInfo() {
        try {
            HttpServletRequest request = ServletUtils.getRequest();
            if (request == null) {
                return "";
            }
            
            // 尝试从请求中获取文件信息
            if (request instanceof org.springframework.web.multipart.support.StandardMultipartHttpServletRequest) {
                org.springframework.web.multipart.support.StandardMultipartHttpServletRequest multipartRequest = 
                    (org.springframework.web.multipart.support.StandardMultipartHttpServletRequest) request;
                
                MultipartFile file = multipartRequest.getFile("file");
                if (file != null && !file.isEmpty()) {
                    return String.format("文件名=%s, 大小=%s字节", 
                        file.getOriginalFilename(), 
                        formatFileSize(file.getSize()));
                }
            }
            
            return "";
        } catch (Exception e) {
            log.debug("获取导入文件信息失败", e);
            return "";
        }
    }

    /**
     * 获取导入参数信息
     */
    private String getImportParams() {
        try {
            HttpServletRequest request = ServletUtils.getRequest();
            if (request == null) {
                return "";
            }
            
            StringBuilder params = new StringBuilder();
            
            // 获取updateSupport参数
            String updateSupport = request.getParameter("updateSupport");
            if (StringUtils.isNotEmpty(updateSupport)) {
                params.append("更新支持=").append("true".equals(updateSupport) ? "是" : "否");
            }
            
            // 获取其他可能的导入参数
            String category = request.getParameter("category");
            if (StringUtils.isNotEmpty(category)) {
                if (params.length() > 0) params.append(", ");
                params.append("分类=").append(category);
            }
            
            return params.toString();
        } catch (Exception e) {
            log.debug("获取导入参数失败", e);
            return "";
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + "";
        } else if (size < 1024 * 1024) {
            return String.format("%.1fKB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1fMB", size / (1024.0 * 1024));
        } else {
            return String.format("%.1fGB", size / (1024.0 * 1024 * 1024));
        }
    }
}
