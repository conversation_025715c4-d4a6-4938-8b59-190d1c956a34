package org.dromara.system.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.sse.dto.SseMessageDto;
import org.dromara.common.sse.utils.SseMessageUtils;
import org.dromara.system.domain.SysLogininfor;
import org.dromara.system.domain.SysOperLog;
import org.dromara.system.domain.SysLogArchiveConfig;
import org.dromara.system.mapper.SysLogininforMapper;
import org.dromara.system.mapper.SysOperLogMapper;
import org.dromara.system.mapper.SysLogArchiveConfigMapper;
import org.dromara.system.service.ISysLogArchiveService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SysLogArchiveServiceImpl implements ISysLogArchiveService {

    private final SysLogininforMapper logininforMapper;
    private final SysOperLogMapper operLogMapper;
    private final SysLogArchiveConfigMapper configMapper;

    /** system用户ID */
    private static final Long SYSTEM_USER_ID = 2L;

    @Override
    @Transactional
    public void checkAndArchiveLogs() {
        SysLogArchiveConfig config = getArchiveConfig();
        if (config == null || !"1".equals(config.getAutoArchive())) {
            return;
        }

        // 计算时间范围：保留时间到2倍保留时间的数据进行转存
        Date retainDate = DateUtils.addMonths(new Date(), -config.getRetainMonths());
        Date archiveDate = DateUtils.addMonths(new Date(), -config.getRetainMonths() * 2);

        // 检查是否有需要转存的数据
        if (hasDataToArchive(archiveDate, retainDate)) {
            archiveLoginLogs(archiveDate, retainDate, config.getArchivePath());
            archiveOperLogs(archiveDate, retainDate, config.getArchivePath());

            // 发送转存完成通知
            sendNotificationMessage("日志转存完成",
                String.format("已自动完成 %s 到 %s 期间的日志转存",
                    DateUtil.format(archiveDate, "yyyy-MM-dd"),
                    DateUtil.format(retainDate, "yyyy-MM-dd")));
        }

        config.setLastArchiveTime(new DateTime());
        configMapper.updateById(config);
    }

    private SysLogArchiveConfig getArchiveConfig() {
        List<SysLogArchiveConfig> configs = configMapper.selectList(null);
        return configs.isEmpty() ? null : configs.get(0);
    }

    /**
     * 手动日志转存
     */
    @Override
    public void manualArchiveLogs() {
        SysLogArchiveConfig config = getArchiveConfig();
        if (config == null) {
            throw new RuntimeException("请先配置日志转存参数");
        }

        Date retainDate = DateUtils.addMonths(new Date(), -config.getRetainMonths());
        Date archiveDate = DateUtils.addMonths(new Date(), -config.getRetainMonths() * 2);

        archiveLoginLogs(archiveDate, retainDate, config.getArchivePath());
        archiveOperLogs(archiveDate, retainDate, config.getArchivePath());

        // 发送手动转存完成通知
        sendNotificationMessage("手动日志转存完成",
            String.format("已手动完成 %s 到 %s 期间的日志转存",
                DateUtil.format(archiveDate, "yyyy-MM-dd"),
                DateUtil.format(retainDate, "yyyy-MM-dd")));

        config.setLastArchiveTime(new DateTime());
        configMapper.updateById(config);
    }

    private boolean hasDataToArchive(Date startDate, Date endDate) {
        // 检查登录日志
        Long loginCount = logininforMapper.selectCount(
            new LambdaQueryWrapper<SysLogininfor>()
                .between(SysLogininfor::getLoginTime, startDate, endDate)
        );

        // 检查操作日志
        Long operCount = operLogMapper.selectCount(
            new LambdaQueryWrapper<SysOperLog>()
                .between(SysOperLog::getOperTime, startDate, endDate)
        );

        return (loginCount + operCount) > 0;
    }

    private void archiveLoginLogs(Date startDate, Date endDate, String archivePath) {
        List<SysLogininfor> logs = logininforMapper.selectList(
            new LambdaQueryWrapper<SysLogininfor>()
                .between(SysLogininfor::getLoginTime, startDate, endDate)
                .orderByAsc(SysLogininfor::getLoginTime)
        );

        if (!logs.isEmpty()) {
            String fileName = String.format("login_logs_%s_to_%s.csv",
                DateUtil.format(startDate, "yyyyMMdd"),
                DateUtil.format(endDate, "yyyyMMdd"));

            exportToCsv(logs, archivePath, fileName, "login");

            // 删除已转存的数据
            logininforMapper.delete(
                new LambdaQueryWrapper<SysLogininfor>()
                    .between(SysLogininfor::getLoginTime, startDate, endDate)
            );

            log.info("登录日志转存完成，共转存{}条记录", logs.size());
        }
    }

    private void archiveOperLogs(Date startDate, Date endDate, String archivePath) {
        List<SysOperLog> logs = operLogMapper.selectList(
            new LambdaQueryWrapper<SysOperLog>()
                .between(SysOperLog::getOperTime, startDate, endDate)
                .orderByAsc(SysOperLog::getOperTime)
        );

        if (!logs.isEmpty()) {
            String fileName = String.format("oper_logs_%s_to_%s.csv",
                DateUtil.format(startDate, "yyyyMMdd"),
                DateUtil.format(endDate, "yyyyMMdd"));

            exportToCsv(logs, archivePath, fileName, "oper");

            // 删除已转存的数据
            operLogMapper.delete(
                new LambdaQueryWrapper<SysOperLog>()
                    .between(SysOperLog::getOperTime, startDate, endDate)
            );

            log.info("操作日志转存完成，共转存{}条记录", logs.size());
        }
    }

    private void exportToCsv(List<?> logs, String archivePath, String fileName, String type) {
        exportToCsv(logs, archivePath, fileName, type, 1000); // 默认1000行flush
    }

    private void exportToCsv(List<?> logs, String archivePath, String fileName, String type, int flushInterval) {
        try {
            File dir = new File(archivePath);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 使用UTF-8 with BOM格式（解决Excel中文乱码）
            File file = new File(dir, fileName);
            try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8))) {

                // 写入BOM头（让Excel识别UTF-8）
                writer.write("\uFEFF");

                // 写入CSV头部
                if ("login".equals(type)) {
                    writer.write(escapeCsvLine(new String[]{
                        "访问ID", "租户编号", "用户账号", "客户端",
                        "设备类型", "登录IP", "登录地点", "浏览器",
                        "操作系统", "登录状态", "提示消息", "访问时间"
                    }));

                    // 添加行计数器
                    int lines = 0;
                    for (Object obj : logs) {
                        SysLogininfor log = (SysLogininfor) obj;
                        writer.write(escapeCsvLine(new Object[]{
                            log.getInfoId(), log.getTenantId(), log.getUserName(),
                            log.getClientKey(), log.getDeviceType(), log.getIpaddr(),
                            log.getLoginLocation(), log.getBrowser(), log.getOs(),
                            log.getStatus(), log.getMsg(), log.getLoginTime()
                        }));

                        if (++lines % flushInterval == 0) {
                            writer.flush();
                        }
                    }
                } else {
                    writer.write(escapeCsvLine(new String[]{
                        "日志主键", "租户编号", "模块标题", "业务类型",
                        "方法名称", "请求方式", "操作类别", "操作人员",
                        "部门名称", "请求URL", "主机地址", "操作地点",
                        "请求参数", "返回参数", "操作状态", "错误消息",
                        "操作时间", "消耗时间", "操作详情"
                    }));

                    // 添加行计数器
                    int lines = 0;
                    for (Object obj : logs) {
                        SysOperLog log = (SysOperLog) obj;
                        writer.write(escapeCsvLine(new Object[]{
                            log.getOperId(), log.getTenantId(), log.getTitle(),
                            log.getBusinessType(), log.getMethod(), log.getRequestMethod(),
                            log.getOperatorType(), log.getOperName(), log.getDeptName(),
                            log.getOperUrl(), log.getOperIp(), log.getOperLocation(),
                            log.getOperParam(), log.getJsonResult(), log.getStatus(),
                            log.getErrorMsg(), log.getOperTime(), log.getCostTime(),
                            log.getDetail()
                        }));

                        if (++lines % flushInterval == 0) {
                            writer.flush();
                        }
                    }
                }
            }
        } catch (IOException e) {
            log.error("导出CSV文件失败", e);
            throw new RuntimeException("导出CSV文件失败: " + e.getMessage());
        }
    }

    /**
     * 将整行数据转换为CSV格式（自动处理转义和逗号分隔）
     */
    private String escapeCsvLine(Object[] fields) {
        return Arrays.stream(fields)
            .map(field -> {
                if (field == null) return "";
                String str = field.toString();
                // 处理包含逗号、换行、引号的情况
                if (str.contains(",") || str.contains("\n") || str.contains("\"")) {
                    return "\"" + str.replace("\"", "\"\"") + "\"";
                }
                return str;
            })
            .collect(Collectors.joining(",")) + "\n";
    }


    private void sendNotificationMessage(String title, String message) {
        try {
            SseMessageDto dto = new SseMessageDto();
            dto.setMessage(message);
            SseMessageUtils.publishMessage(dto);
        } catch (Exception e) {
            log.error("发送通知消息失败", e);
        }
    }
}
