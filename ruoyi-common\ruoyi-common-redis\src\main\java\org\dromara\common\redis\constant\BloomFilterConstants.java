package org.dromara.common.redis.constant;

public class BloomFilterConstants {
    /**
     * 通知公告布隆过滤器前缀
     */
    public static final String NOTICE_READ_KEY = "bloom:notice:read:";

    /**
     * 通知公告保留天数
     */
    public static final int NOTICE_RETAIN_DAYS = 7;

    /**
     * 默认的误判率
     */
    public static final double DEFAULT_FALSE_PROBABILITY = 0.01;

    /**
     * 预期插入数量
     */
    public static final long DEFAULT_EXPECTED_INSERTIONS = 10000;


}

