package org.dromara.common.log.strategy;

import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.log.content.OperatorLogContentUtil;
import org.dromara.common.log.content.OperatorLogMetaData;
import org.dromara.common.log.content.OperatorLogMetaDataBuilder;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.log.utils.OperatorLogUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 其他通用类型日志内容模板
 * <AUTHOR>
 */
@Component
public class DefaultOperatorLogStrategy implements IOperateLogStrategy {

    @Override
    public String getOperatorContent(Object requestObj, Class<?>[] tableEntity, String operatorObjId) {
        try {
            String objectType = OperatorLogUtil.getTableComment(tableEntity[0]);
            String businessType = getBusinessTypeDesc();

            StringBuilder content = new StringBuilder();
            content.append("进行了[").append(businessType).append("]操作");

            // 如果有操作对象id
            if (StringUtils.isNotEmpty(operatorObjId)) {
                content.append("，操作ID：").append(operatorObjId);
            }

            // 如果有操作对象，列举操作对象详细属性
            if (requestObj != null) {
                OperatorLogMetaDataBuilder builder = new OperatorLogMetaDataBuilder();
                OperatorLogMetaData metaData = builder.getChangeModel(requestObj, tableEntity[0]);

                List<String> fieldContents = new ArrayList<>();
                Map<String, String> fieldMap = metaData.getFieldMap();

                for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
                    String fieldContent = OperatorLogMetaDataBuilder.getColumnCommentAndDictValue(
                        tableEntity[0],
                        entry.getKey(),
                        entry.getValue()
                    );
                    fieldContents.add(fieldContent);
                }

                content.append("，操作数据：[").append(String.join(";", fieldContents)).append("]");
            }

            return content.toString();
        } catch (Exception e) {
            return "执行其他操作，操作ID：" + operatorObjId;
        }
    }

    private String formatValue(Object value) {
        if (value == null) {
            return "null";
        }
        if (value.getClass().isArray()) {
            if (value instanceof Long[]) {
                return Arrays.toString((Long[]) value);
            }
            return Arrays.toString((Object[]) value);
        }
        return value.toString();
    }

    private String getBusinessTypeDesc() {
        // DefaultOperatorLogStrategy主要处理那些没有专门策略的业务类型
        // 由于大部分业务类型都有专门的策略，这里主要返回"其他"
        return "其他";
    }
}


