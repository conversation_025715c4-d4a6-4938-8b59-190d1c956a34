package org.dromara.cssrcSoftware.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.cssrcSoftware.domain.vo.SoftwareInfoVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareInfoBo;
import org.dromara.cssrcSoftware.service.ISoftwareInfoService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 软件基本信息
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/cssrc/cssrcSoftwareInfo")
public class SoftwareInfoController extends BaseController {

    private final ISoftwareInfoService softwareInfoService;

    /**
     * 查询软件基本信息列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareInfo:list")
    @GetMapping("/list")
    public TableDataInfo<SoftwareInfoVo> list(SoftwareInfoBo bo, PageQuery pageQuery) {
        return softwareInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出软件基本信息列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareInfo:export")
    @Log(title = "软件基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SoftwareInfoBo bo, HttpServletResponse response) {
        List<SoftwareInfoVo> list = softwareInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "软件基本信息", SoftwareInfoVo.class, response);
    }

    /**
     * 获取软件基本信息详细信息
     *
     * @param softwareId 主键
     */
    @SaCheckPermission("cssrc:cssrcSoftwareInfo:query")
    @GetMapping("/{softwareId}")
    public R<SoftwareInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long softwareId) {
        return R.ok(softwareInfoService.queryById(softwareId));
    }

    /**
     * 新增软件基本信息
     */
    @SaCheckPermission("cssrc:cssrcSoftwareInfo:add")
    @Log(title = "软件基本信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SoftwareInfoBo bo) {
        return toAjax(softwareInfoService.insertByBo(bo));
    }

    /**
     * 修改软件基本信息
     */
    @SaCheckPermission("cssrc:cssrcSoftwareInfo:edit")
    @Log(title = "软件基本信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SoftwareInfoBo bo) {
        return toAjax(softwareInfoService.updateByBo(bo));
    }

    /**
     * 删除软件基本信息
     *
     * @param softwareIds 主键串
     */
    @SaCheckPermission("cssrc:cssrcSoftwareInfo:remove")
    @Log(title = "软件基本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{softwareIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] softwareIds) {
        return toAjax(softwareInfoService.deleteWithValidByIds(List.of(softwareIds), true));
    }

    // ==================== 用户侧接口（无权限验证） ====================

    /**
     * 用户侧查询软件基本信息列表（无权限验证）
     */
    @GetMapping("/public/list")
    public TableDataInfo<SoftwareInfoVo> publicList(SoftwareInfoBo bo, PageQuery pageQuery) {
        // 只查询正常状态的软件
        bo.setStatus("0");
        return softwareInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 用户侧获取软件基本信息详细信息（无权限验证）
     *
     * @param softwareId 主键
     */
    @GetMapping("/public/{softwareId}")
    public R<SoftwareInfoVo> publicGetInfo(@NotNull(message = "主键不能为空")
                                          @PathVariable Long softwareId) {
        SoftwareInfoVo vo = softwareInfoService.queryById(softwareId);
        if (vo == null || !"0".equals(vo.getStatus())) {
            return R.fail("软件不存在或已停用");
        }
        return R.ok(vo);
    }

    /**
     * 用户侧根据分类查询软件列表（无权限验证）
     *
     * @param categoryId 分类ID
     */
    @GetMapping("/public/category/{categoryId}")
    public TableDataInfo<SoftwareInfoVo> publicListByCategory(@PathVariable Long categoryId, PageQuery pageQuery) {
        SoftwareInfoBo bo = new SoftwareInfoBo();
        bo.setCategoryId(categoryId);
        bo.setStatus("0");
        return softwareInfoService.queryPageList(bo, pageQuery);
    }
}
