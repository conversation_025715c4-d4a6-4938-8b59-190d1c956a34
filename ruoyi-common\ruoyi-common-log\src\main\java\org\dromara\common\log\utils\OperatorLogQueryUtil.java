package org.dromara.common.log.utils;


import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;

import java.lang.reflect.Method;

public class OperatorLogQueryUtil {

    private OperatorLogQueryUtil(){}

    // 这部分要写一个复杂的规则进行获取适配，现在粗略写了一个
    private static String[] getMethodNamePatterns(String className) {
        String entityName = className.replace("Sys", "");
        return new String[]{
            "getById",
            "select" + entityName + "ById",
            "get" + entityName + "ById",
            "queryById"
        };
    }


    /**
     * 通过Id查询获取单个数据
     */
    public static Object getObj(String operatorObjId, Class<?> tableEntity) {
        String className = tableEntity.getSimpleName();
        String serviceName = className + "ServiceImpl";
        serviceName = StringUtils.uncapitalize(serviceName);

        // 检查是否存在对应的Service bean
        if (!SpringUtils.containsBean(serviceName)) {
            // 对于没有Service的实体类（如关联表），返回null或创建一个简单对象
            return handleEntityWithoutService(operatorObjId, tableEntity);
        }

        Object service = SpringUtils.getBean(serviceName);
        if (service == null) {
            return handleEntityWithoutService(operatorObjId, tableEntity);
        }

        String[] methodPatterns = getMethodNamePatterns(className);
        for (String methodName : methodPatterns) {
            try {
                Method method = service.getClass().getMethod(methodName, Long.class);
                return method.invoke(service, Long.parseLong(operatorObjId));
            } catch (NoSuchMethodException e) {
                continue;
            } catch (Exception e) {
                throw new RuntimeException("获取对象失败: " + e.getMessage(), e);
            }
        }
        return handleEntityWithoutService(operatorObjId, tableEntity);
    }

    /**
     * 处理没有Service的实体类
     */
    private static Object handleEntityWithoutService(String operatorObjId, Class<?> tableEntity) {
        try {
            // 对于关联表等没有Service的实体，创建一个简单的对象用于日志记录
            Object instance = tableEntity.getDeclaredConstructor().newInstance();

            // 尝试设置ID字段
            try {
                java.lang.reflect.Field[] fields = tableEntity.getDeclaredFields();
                for (java.lang.reflect.Field field : fields) {
                    field.setAccessible(true);
                    if (field.getName().toLowerCase().contains("id") &&
                        (field.getType() == Long.class || field.getType() == long.class)) {
                        field.set(instance, Long.parseLong(operatorObjId));
                        break;
                    }
                }
            } catch (Exception e) {
                // 忽略设置ID失败的情况
            }

            return instance;
        } catch (Exception e) {
            // 如果无法创建实例，返回null
            return null;
        }
    }

}
