package org.dromara.cssrcSoftware.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.dromara.cssrcSoftware.domain.bo.*;
import org.dromara.cssrcSoftware.domain.vo.*;
import org.dromara.cssrcSoftware.service.*;
import org.dromara.system.service.ISysOssService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 软件资源库用户侧公开接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/public/software")
@SaIgnore
public class SoftwarePublicController extends BaseController {

    private final ISoftwareCategoryService softwareCategoryService;
    private final ISoftwareInfoService softwareInfoService;
    private final ISoftwareVersionService softwareVersionService;
    private final ISoftwareDownloadLogService softwareDownloadLogService;
    private final ISoftwareCommentService softwareCommentService;
    private final ISoftwareCommentLikeService softwareCommentLikeService;
    private final ISysOssService ossService;

    /**
     * 获取软件分类树（公开接口）
     */
    @GetMapping("/categories")
    public R<List<SoftwareCategoryVo>> getCategoryTree() {
        SoftwareCategoryBo bo = new SoftwareCategoryBo();
        bo.setStatus("0"); // 只查询正常状态的分类
        List<SoftwareCategoryVo> list = softwareCategoryService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 根据分类ID获取软件列表（公开接口）
     */
    @GetMapping("/list")
    public TableDataInfo<SoftwareInfoVo> getSoftwareList(
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String softwareName,
            PageQuery pageQuery) {
        SoftwareInfoBo bo = new SoftwareInfoBo();
        bo.setCategoryId(categoryId);
        bo.setSoftwareName(softwareName);
        bo.setStatus("0"); // 只查询正常状态的软件
        return softwareInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取软件详情（公开接口）
     */
    @GetMapping("/{softwareId}")
    public R<SoftwareInfoVo> getSoftwareDetail(@PathVariable Long softwareId) {
        SoftwareInfoVo vo = softwareInfoService.queryById(softwareId);
        if (vo == null || !"0".equals(vo.getStatus())) {
            return R.fail("软件不存在或已停用");
        }
        return R.ok(vo);
    }

    /**
     * 获取软件版本列表（公开接口）
     */
    @GetMapping("/{softwareId}/versions")
    public R<List<SoftwareVersionVo>> getSoftwareVersions(@PathVariable Long softwareId) {
        SoftwareVersionBo bo = new SoftwareVersionBo();
        bo.setSoftwareId(softwareId);
        bo.setStatus("0"); // 只查询正常状态的版本
        bo.setDisplayStatus("0"); // 只查询前台显示的版本
        bo.setApprovalStatus("finish"); // 只查询审批通过的版本
        List<SoftwareVersionVo> list = softwareVersionService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 获取软件版本详情（公开接口）
     */
    @GetMapping("/version/{versionId}")
    public R<SoftwareVersionVo> getVersionDetail(@PathVariable Long versionId) {
        SoftwareVersionVo vo = softwareVersionService.queryById(versionId);
        if (vo == null || !"0".equals(vo.getStatus()) ||
            !"0".equals(vo.getDisplayStatus()) || !"finish".equals(vo.getApprovalStatus())) {
            return R.fail("软件版本不存在或不可用");
        }
        return R.ok(vo);
    }

    /**
     * 下载软件版本文件（公开接口）
     */
    @GetMapping("/download/{versionId}")
    public void downloadSoftware(@PathVariable Long versionId, HttpServletResponse response) throws IOException {
        // 获取版本信息
        SoftwareVersionVo version = softwareVersionService.queryById(versionId);
        if (version == null || !"0".equals(version.getStatus()) ||
            !"0".equals(version.getDisplayStatus()) || !"finish".equals(version.getApprovalStatus())) {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return;
        }

        // 记录下载日志（先记录为开始状态）
        SoftwareDownloadLogBo logBo = new SoftwareDownloadLogBo();
        logBo.setSoftwareId(version.getSoftwareId());
        logBo.setVersionId(versionId);

        // 尝试获取当前用户ID，如果未登录则使用默认值
        LoginUser loginUser = LoginHelper.getLoginUser();

        logBo.setUserId(loginUser != null ? loginUser.getUserId() : 0L);
        logBo.setIpAddress(ServletUtils.getClientIP());
        logBo.setDownloadTime(new Date());
        logBo.setStatus("1"); // 先设置为下载中状态
        Boolean insertResult = softwareDownloadLogService.insertByBo(logBo);

        try {
            // 更新下载次数
            SoftwareVersionBo updateBo = new SoftwareVersionBo();
            updateBo.setVersionId(versionId);
            updateBo.setDownloadCount((version.getDownloadCount() != null ? version.getDownloadCount() : 0) + 1);
            softwareVersionService.updateByBo(updateBo);

            // 调用OSS下载接口
            ossService.download(version.getOssId(), response);

            // 下载成功后更新日志状态
            if (insertResult && logBo.getLogId() != null) {
                logBo.setStatus("0");
                softwareDownloadLogService.updateByBo(logBo);
            }

        } catch (Exception e) {
            // 下载失败时更新日志状态
            if (insertResult && logBo.getLogId() != null) {
                logBo.setStatus("1");
                softwareDownloadLogService.updateByBo(logBo);
            }
            throw e;
        }
    }

    /**
     * 获取软件评论列表（公开接口）
     */
    @GetMapping("/{versionId}/comments")
    public TableDataInfo<SoftwareCommentVo> getSoftwareComments(
            @PathVariable Long versionId,
            PageQuery pageQuery) {
        SoftwareCommentBo bo = new SoftwareCommentBo();
        bo.setVersionId(versionId);
        bo.setStatus("0"); // 只查询正常状态的评论
        return softwareCommentService.queryPageList(bo, pageQuery);
    }

    /**
     * 添加软件评论
     */
    @PostMapping("/{versionId}/comments")
    public R<Void> addSoftwareComment(@PathVariable Long versionId, @RequestBody SoftwareCommentBo bo) {
        // 尝试获取用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return R.fail("请先登录");
        }

        // 填充信息
        bo.setVersionId(versionId);
        bo.setUserId(loginUser.getUserId());
        bo.setStatus("0");
        return toAjax(softwareCommentService.insertByBo(bo));
    }

    /**
     * 回复评论（需要登录）
     */
    @PostMapping("/comments/{commentId}/reply")
    public R<Void> replyComment(@PathVariable Long commentId, @RequestBody SoftwareCommentBo bo) {
        // 尝试获取用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return R.fail("请先登录");
        }

        bo.setReplyTo(commentId);
        bo.setUserId(loginUser.getUserId());
        bo.setStatus("0");
        return toAjax(softwareCommentService.insertByBo(bo));
    }

    /**
     * 点赞/取消点赞评论（需要登录）
     */
    @PostMapping("/comments/{commentId}/like")
    public R<Void> toggleCommentLike(@PathVariable Long commentId, @RequestParam(defaultValue = "1") Integer likeType) {
        // 尝试获取用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return R.fail("请先登录");
        }

        Long userId = loginUser.getUserId();
        Long likeTypeLong = likeType.longValue();

        // 检查是否已经点赞/点踩
        SoftwareCommentLikeBo queryBo = new SoftwareCommentLikeBo();
        queryBo.setCommentId(commentId);
        queryBo.setUserId(userId);
        List<SoftwareCommentLikeVo> existingLikes = softwareCommentLikeService.queryList(queryBo);

        if (!existingLikes.isEmpty()) {
            // 如果已存在相同类型的点赞，则取消
            SoftwareCommentLikeVo existing = existingLikes.get(0);
            if (existing.getLikeType().equals(likeTypeLong)) {
                softwareCommentLikeService.deleteWithValidByIds(List.of(existing.getId()), false);
                return R.ok("取消成功");
            } else {
                // 如果存在不同类型的点赞，则更新
                SoftwareCommentLikeBo updateBo = new SoftwareCommentLikeBo();
                updateBo.setId(existing.getId());
                updateBo.setLikeType(likeTypeLong);
                softwareCommentLikeService.updateByBo(updateBo);
                return R.ok("更新成功");
            }
        } else {
            // 新增点赞
            SoftwareCommentLikeBo insertBo = new SoftwareCommentLikeBo();
            insertBo.setCommentId(commentId);
            insertBo.setUserId(userId);
            insertBo.setLikeType(likeTypeLong);
            softwareCommentLikeService.insertByBo(insertBo);
            return R.ok("点赞成功");
        }
    }

    /**
     * 获取评论点赞状态（需要登录）
     */
    @GetMapping("/comments/{commentId}/like-status")
    public R<SoftwareCommentLikeVo> getCommentLikeStatus(@PathVariable Long commentId) {
        // 尝试获取用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return R.fail("请先登录");
        }

        Long userId = loginUser.getUserId();
        SoftwareCommentLikeBo queryBo = new SoftwareCommentLikeBo();
        queryBo.setCommentId(commentId);
        queryBo.setUserId(userId);
        List<SoftwareCommentLikeVo> likes = softwareCommentLikeService.queryList(queryBo);

        return R.ok(likes.isEmpty() ? null : likes.get(0));
    }

    /**
     * 获取用户下载历史（需要登录）
     */
    @GetMapping("/download-history")
    public TableDataInfo<SoftwareDownloadLogVo> getUserDownloadHistory(PageQuery pageQuery) {
        // 尝试获取用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return TableDataInfo.build();
        }

        Long userId = loginUser.getUserId();
        SoftwareDownloadLogBo queryBo = new SoftwareDownloadLogBo();
        queryBo.setUserId(userId);
        return softwareDownloadLogService.queryPageList(queryBo, pageQuery);
    }

    /**
     * 搜索软件（公开接口）
     */
    @GetMapping("/search")
    public TableDataInfo<SoftwareInfoVo> searchSoftware(
            @RequestParam String keyword,
            PageQuery pageQuery) {
        SoftwareInfoBo bo = new SoftwareInfoBo();
        bo.setSoftwareName(keyword);
        bo.setStatus("0"); // 只查询正常状态的软件
        return softwareInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取软件下载统计（公开接口）
     */
    @GetMapping("/statistics")
    public R<Map<String, Object>> getSoftwareStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 获取软件总数
        SoftwareInfoBo infoBo = new SoftwareInfoBo();
        infoBo.setStatus("0");
        List<SoftwareInfoVo> allSoftware = softwareInfoService.queryList(infoBo);
        statistics.put("totalSoftware", allSoftware.size());

        // 获取版本总数
        SoftwareVersionBo versionBo = new SoftwareVersionBo();
        versionBo.setStatus("0");
        versionBo.setDisplayStatus("0");
        versionBo.setApprovalStatus("finish");
        List<SoftwareVersionVo> allVersions = softwareVersionService.queryList(versionBo);
        statistics.put("totalVersions", allVersions.size());

        // 获取总下载次数
        long totalDownloads = allSoftware.stream()
            .mapToLong(s -> s.getDownloadCount() != null ? s.getDownloadCount() : 0L)
            .sum();
        statistics.put("totalDownloads", totalDownloads);

        // 获取分类数量
        SoftwareCategoryBo categoryBo = new SoftwareCategoryBo();
        categoryBo.setStatus("0");
        List<SoftwareCategoryVo> allCategories = softwareCategoryService.queryList(categoryBo);
        statistics.put("totalCategories", allCategories.size());

        return R.ok(statistics);
    }

    /**
     * 获取热门软件排行（公开接口）
     */
    @GetMapping("/popular")
    public R<List<SoftwareInfoVo>> getPopularSoftware(@RequestParam(defaultValue = "10") Integer limit) {
        SoftwareInfoBo bo = new SoftwareInfoBo();
        bo.setStatus("0");
        List<SoftwareInfoVo> allSoftware = softwareInfoService.queryList(bo);

        // 按下载次数排序并限制数量
        List<SoftwareInfoVo> popularSoftware = allSoftware.stream()
            .sorted((a, b) -> Long.compare(
                b.getDownloadCount() != null ? b.getDownloadCount() : 0L,
                a.getDownloadCount() != null ? a.getDownloadCount() : 0L
            ))
            .limit(limit)
            .collect(Collectors.toList());

        return R.ok(popularSoftware);
    }
}
