package org.dromara.cssrcSoftware.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.cssrcSoftware.domain.vo.SoftwareCommentVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareCommentBo;
import org.dromara.cssrcSoftware.service.ISoftwareCommentService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 软件评论
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/cssrc/cssrcSoftwareComment")
public class SoftwareCommentController extends BaseController {

    private final ISoftwareCommentService softwareCommentService;

    /**
     * 查询软件评论列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareComment:list")
    @GetMapping("/list")
    public TableDataInfo<SoftwareCommentVo> list(SoftwareCommentBo bo, PageQuery pageQuery) {
        return softwareCommentService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出软件评论列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareComment:export")
    @Log(title = "软件评论", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SoftwareCommentBo bo, HttpServletResponse response) {
        List<SoftwareCommentVo> list = softwareCommentService.queryList(bo);
        ExcelUtil.exportExcel(list, "软件评论", SoftwareCommentVo.class, response);
    }

    /**
     * 获取软件评论详细信息
     *
     * @param commentId 主键
     */
    @SaCheckPermission("cssrc:cssrcSoftwareComment:query")
    @GetMapping("/{commentId}")
    public R<SoftwareCommentVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long commentId) {
        return R.ok(softwareCommentService.queryById(commentId));
    }

    /**
     * 新增软件评论
     */
    @SaCheckPermission("cssrc:cssrcSoftwareComment:add")
    @Log(title = "软件评论", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SoftwareCommentBo bo) {
        return toAjax(softwareCommentService.insertByBo(bo));
    }

    /**
     * 修改软件评论
     */
    @SaCheckPermission("cssrc:cssrcSoftwareComment:edit")
    @Log(title = "软件评论", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SoftwareCommentBo bo) {
        return toAjax(softwareCommentService.updateByBo(bo));
    }

    /**
     * 删除软件评论
     *
     * @param commentIds 主键串
     */
    @SaCheckPermission("cssrc:cssrcSoftwareComment:remove")
    @Log(title = "软件评论", businessType = BusinessType.DELETE)
    @DeleteMapping("/{commentIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] commentIds) {
        return toAjax(softwareCommentService.deleteWithValidByIds(List.of(commentIds), true));
    }

    // ==================== 用户侧接口（无权限验证） ====================

    /**
     * 用户侧查询软件评论列表（无权限验证）
     */
    @GetMapping("/public/list")
    public TableDataInfo<SoftwareCommentVo> publicList(SoftwareCommentBo bo, PageQuery pageQuery) {
        // 只查询正常状态的评论
        bo.setStatus("0");
        return softwareCommentService.queryPageList(bo, pageQuery);
    }

    /**
     * 用户侧根据软件ID查询评论列表（无权限验证）
     *
     * @param versionId 软件ID
     */
    @GetMapping("/public/software/{versionId}")
    public TableDataInfo<SoftwareCommentVo> publicListBySoftware(@PathVariable Long versionId, PageQuery pageQuery) {
        SoftwareCommentBo bo = new SoftwareCommentBo();
        bo.setVersionId(versionId);
        bo.setStatus("0");
        return softwareCommentService.queryPageList(bo, pageQuery);
    }

    /**
     * 用户侧新增软件评论（无权限验证）
     */
    @Log(title = "软件评论", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/public")
    public R<Void> publicAdd(@Validated(AddGroup.class) @RequestBody SoftwareCommentBo bo) {
        // 设置默认状态为正常
        bo.setStatus("0");
        return toAjax(softwareCommentService.insertByBo(bo));
    }
}
