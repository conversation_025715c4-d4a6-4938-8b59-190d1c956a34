package org.dromara.cssrcSoftware.domain.vo;

import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.cssrcSoftware.domain.SoftwareComment;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 软件评论视图对象 software_comment
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SoftwareComment.class)
public class SoftwareCommentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 评论ID
     */
    @ExcelProperty(value = "评论ID")
    private Long commentId;

    /**
     * 版本ID
     */
    @ExcelProperty(value = "版本ID")
    private Long versionId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 用户工号
     */
    @ExcelProperty(value = "工号")
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "userId")
    private String userName;

    /**
     * 用户姓名
     */
    @ExcelProperty(value = "姓名")
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "userId")
    private String nickName;

    /**
     * 评论内容
     */
    @ExcelProperty(value = "评论内容")
    private String content;

    /**
     * 回复哪条评论
     */
    @ExcelProperty(value = "回复哪条评论")
    private Long replyTo;

    /**
     * 状态（0正常 1屏蔽）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=正常,1=屏蔽")
    private String status;


}
