package org.dromara.cssrcSoftware.service;

import org.dromara.cssrcSoftware.domain.vo.SoftwareInfoVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareInfoBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 软件基本信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface ISoftwareInfoService {

    /**
     * 查询软件基本信息
     *
     * @param softwareId 主键
     * @return 软件基本信息
     */
    SoftwareInfoVo queryById(Long softwareId);

    /**
     * 分页查询软件基本信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 软件基本信息分页列表
     */
    TableDataInfo<SoftwareInfoVo> queryPageList(SoftwareInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的软件基本信息列表
     *
     * @param bo 查询条件
     * @return 软件基本信息列表
     */
    List<SoftwareInfoVo> queryList(SoftwareInfoBo bo);

    /**
     * 新增软件基本信息
     *
     * @param bo 软件基本信息
     * @return 是否新增成功
     */
    Boolean insertByBo(SoftwareInfoBo bo);

    /**
     * 修改软件基本信息
     *
     * @param bo 软件基本信息
     * @return 是否修改成功
     */
    Boolean updateByBo(SoftwareInfoBo bo);

    /**
     * 校验并批量删除软件基本信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
