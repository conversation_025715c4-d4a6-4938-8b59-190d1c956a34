package org.dromara.cssrcSoftware.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.dromara.cssrcSoftware.domain.SoftwareComment;
import org.dromara.cssrcSoftware.domain.vo.SoftwareCommentVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 软件评论Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@DS("software")
public interface SoftwareCommentMapper extends BaseMapperPlus<SoftwareComment, SoftwareCommentVo> {

}
