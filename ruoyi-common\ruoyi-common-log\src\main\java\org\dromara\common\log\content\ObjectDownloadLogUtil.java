package org.dromara.common.log.content;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 文件下载日志内容生成器
 * <AUTHOR>
 */
public class ObjectDownloadLogUtil {

    public String generatorContent(Object object, String objectType, Class<?> entityClass) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        // 获取操作数据元数据
        OperatorLogMetaDataBuilder builder = new OperatorLogMetaDataBuilder();
        OperatorLogMetaData metaData = builder.getChangeModel(object, entityClass);

        // 生成文件下载字段内容
        List<String> fieldContents = new ArrayList<>();
        Map<String, String> fieldMap = metaData.getFieldMap();

        for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
            String fieldContent = OperatorLogMetaDataBuilder.getColumnCommentAndDictValue(
                entityClass,
                entry.getKey(),
                entry.getValue()
            );
            fieldContents.add(fieldContent);
        }

        String content = String.join(";", fieldContents);
        return OperatorLogContentUtil.downloadObjFormat(metaData.getName(), objectType, content);
    }


    /**
     * 格式化值
     * @param value
     * @return
     */
    private String formatValue(Object value) {
        if (value == null) {
            return "null";
        }
        if (value.getClass().isArray()) {
            if (value instanceof Long[]) {
                return Arrays.toString((Long[]) value);
            }
            return Arrays.toString((Object[]) value);
        }
        return value.toString();
    }

}
