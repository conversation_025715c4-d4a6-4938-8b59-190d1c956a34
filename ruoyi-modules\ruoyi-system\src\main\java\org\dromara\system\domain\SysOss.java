package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import org.dromara.common.encrypt.annotation.EncryptField;
import org.dromara.common.tenant.core.TenantEntity;

/**
 * OSS对象存储对象
 *
 * <AUTHOR> Li
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文件存储")
@TableName("sys_oss")
public class SysOss extends TenantEntity {


    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 对象存储主键
     */
    @TableId(value = "oss_id")
    private Long ossId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 原名
     */
    private String originalName;

    /**
     * 文件后缀名
     */
    private String fileSuffix;

    /**
     * URL地址
     */
    private String url;

    /**
     * 扩展字段
     */
    private String ext1;

    /**
     * 服务商
     */
    private String service;

    /**
     * 文件密级
     */
    @EncryptField
    private String fileSecret;
}
