package org.dromara.system.domain.bo;

import cn.hutool.core.date.DateTime;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.system.domain.SysLogArchiveConfig;
import org.dromara.system.domain.SysRole;

import java.util.Date;

/**
 * 日志转存配置业务对象
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysLogArchiveConfig.class, reverseConvertGenerate = false)
public class SysLogArchiveConfigBo extends BaseEntity {

    @NotNull(message = "配置ID不能为空", groups = { EditGroup.class })
    private Long configId;

    @NotNull(message = "保留月数不能为空")
    private Integer retainMonths;

    /**
     * 转存路径
     */
    @NotBlank(message = "转存路径不能为空")
    private String archivePath;

    /**
     * 自动转存
     */
    private String autoArchive;

    /**
     * 上次日志转存时间
     */
    private Date lastArchiveTime;
}
