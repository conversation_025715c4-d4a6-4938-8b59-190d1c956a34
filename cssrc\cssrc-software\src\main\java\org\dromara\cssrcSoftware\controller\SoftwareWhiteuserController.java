package org.dromara.cssrcSoftware.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.cssrcSoftware.domain.vo.SoftwareWhiteuserVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareWhiteuserBo;
import org.dromara.cssrcSoftware.service.ISoftwareWhiteuserService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 软件权限白名单
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/cssrc/cssrcSoftwareWhiteuser")
public class SoftwareWhiteuserController extends BaseController {

    private final ISoftwareWhiteuserService softwareWhiteuserService;

    /**
     * 查询软件权限白名单列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareWhiteuser:list")
    @GetMapping("/list")
    public TableDataInfo<SoftwareWhiteuserVo> list(SoftwareWhiteuserBo bo, PageQuery pageQuery) {
        return softwareWhiteuserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出软件权限白名单列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareWhiteuser:export")
    @Log(title = "软件权限白名单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SoftwareWhiteuserBo bo, HttpServletResponse response) {
        List<SoftwareWhiteuserVo> list = softwareWhiteuserService.queryList(bo);
        ExcelUtil.exportExcel(list, "软件权限白名单", SoftwareWhiteuserVo.class, response);
    }

    /**
     * 获取软件权限白名单详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("cssrc:cssrcSoftwareWhiteuser:query")
    @GetMapping("/{id}")
    public R<SoftwareWhiteuserVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(softwareWhiteuserService.queryById(id));
    }

    /**
     * 新增软件权限白名单
     */
    @SaCheckPermission("cssrc:cssrcSoftwareWhiteuser:add")
    @Log(title = "软件权限白名单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SoftwareWhiteuserBo bo) {
        return toAjax(softwareWhiteuserService.insertByBo(bo));
    }

    /**
     * 修改软件权限白名单
     */
    @SaCheckPermission("cssrc:cssrcSoftwareWhiteuser:edit")
    @Log(title = "软件权限白名单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SoftwareWhiteuserBo bo) {
        return toAjax(softwareWhiteuserService.updateByBo(bo));
    }

    /**
     * 删除软件权限白名单
     *
     * @param ids 主键串
     */
    @SaCheckPermission("cssrc:cssrcSoftwareWhiteuser:remove")
    @Log(title = "软件权限白名单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(softwareWhiteuserService.deleteWithValidByIds(List.of(ids), true));
    }
}
