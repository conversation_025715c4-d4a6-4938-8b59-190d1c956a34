package org.dromara.cssrcSoftware.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.cssrcSoftware.domain.bo.SoftwareCommentBo;
import org.dromara.cssrcSoftware.domain.vo.SoftwareCommentVo;
import org.dromara.cssrcSoftware.domain.SoftwareComment;
import org.dromara.cssrcSoftware.mapper.SoftwareCommentMapper;
import org.dromara.cssrcSoftware.service.ISoftwareCommentService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 软件评论Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RequiredArgsConstructor
@Service
public class SoftwareCommentServiceImpl implements ISoftwareCommentService {

    private final SoftwareCommentMapper baseMapper;

    /**
     * 查询软件评论
     *
     * @param commentId 主键
     * @return 软件评论
     */
    @Override
    public SoftwareCommentVo queryById(Long commentId){
        return baseMapper.selectVoById(commentId);
    }

    /**
     * 分页查询软件评论列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 软件评论分页列表
     */
    @Override
    public TableDataInfo<SoftwareCommentVo> queryPageList(SoftwareCommentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SoftwareComment> lqw = buildQueryWrapper(bo);
        Page<SoftwareCommentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的软件评论列表
     *
     * @param bo 查询条件
     * @return 软件评论列表
     */
    @Override
    public List<SoftwareCommentVo> queryList(SoftwareCommentBo bo) {
        LambdaQueryWrapper<SoftwareComment> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SoftwareComment> buildQueryWrapper(SoftwareCommentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SoftwareComment> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SoftwareComment::getCommentId);
        lqw.eq(bo.getVersionId() != null, SoftwareComment::getVersionId, bo.getVersionId());
        lqw.eq(bo.getUserId() != null, SoftwareComment::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), SoftwareComment::getContent, bo.getContent());
        lqw.eq(bo.getReplyTo() != null, SoftwareComment::getReplyTo, bo.getReplyTo());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SoftwareComment::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增软件评论
     *
     * @param bo 软件评论
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SoftwareCommentBo bo) {
        SoftwareComment add = MapstructUtils.convert(bo, SoftwareComment.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCommentId(add.getCommentId());
        }
        return flag;
    }

    /**
     * 修改软件评论
     *
     * @param bo 软件评论
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SoftwareCommentBo bo) {
        SoftwareComment update = MapstructUtils.convert(bo, SoftwareComment.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SoftwareComment entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除软件评论信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
