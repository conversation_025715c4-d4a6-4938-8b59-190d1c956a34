package org.dromara.system.domain.vo;

import cn.hutool.core.date.DateTime;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.system.domain.SysLogArchiveConfig;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 日志转存配置视图对象
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysLogArchiveConfig.class)
public class SysLogArchiveConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long configId;

    /**
     * 保留月数
     */
    private Integer retainMonths;

    /**
     * 转存路径
     */
    private String archivePath;

    /**
     * 自动转存
     */
    private String autoArchive;

    /**
     * 上次日志转存时间
     */
    private Date lastArchiveTime;
}
