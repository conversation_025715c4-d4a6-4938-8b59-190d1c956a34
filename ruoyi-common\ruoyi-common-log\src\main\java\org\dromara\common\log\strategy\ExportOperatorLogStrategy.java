package org.dromara.common.log.strategy;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.log.content.OperatorLogMetaDataBuilder;
import org.dromara.common.log.utils.OperatorLogContext;
import org.dromara.common.log.utils.OperatorLogUtil;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.StringJoiner;

/**
 * 导出操作日志生成策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ExportOperatorLogStrategy implements IOperateLogStrategy {

    private static final String[] EXCLUDE_EXPORT_PROPERTIES = {
        "pageNum", "pageSize", "orderByColumn", "isAsc"
    };

    @Override
    public String getOperatorContent(Object requestObj, Class<?>[] tableEntity, String operatorObjId) {
        try {
            String objectType = OperatorLogUtil.getTableComment(tableEntity[0]);
            
            // 获取导出条件
            String exportConditions = getExportConditions(requestObj, tableEntity[0]);
            
            if (StringUtils.isNotEmpty(exportConditions)) {
                return String.format("导出%s数据%s", objectType, exportConditions);
            } else {
                return String.format("导出%s数据", objectType);
            }
        } catch (Exception e) {
            log.error("生成导出操作日志失败", e);
            return "执行了[导出]操作";
        } finally {
            OperatorLogContext.clear();
        }
    }

    /**
     * 获取导出条件描述
     */
    private String getExportConditions(Object requestObj, Class<?> entityClass) {
        try {
            // 优先从请求参数中获取条件
            Map<String, String> paramsMap = ServletUtils.getParamMap(ServletUtils.getRequest());
            
            // 移除分页和排序参数
            for (String excludeProperty : EXCLUDE_EXPORT_PROPERTIES) {
                paramsMap.remove(excludeProperty);
            }
            
            if (!paramsMap.isEmpty()) {
                StringJoiner conditions = new StringJoiner(", ", "，导出条件：", "");
                
                paramsMap.forEach((key, value) -> {
                    if (StringUtils.isNotEmpty(value)) {
                        try {
                            // 获取字段中文名称和字典值转换
                            String fieldContent = OperatorLogMetaDataBuilder.getColumnCommentAndDictValue(
                                entityClass, key, value
                            );
                            conditions.add(fieldContent);
                        } catch (Exception e) {
                            conditions.add(key + "=" + value);
                        }
                    }
                });
                
                return conditions.length() > 0 ? conditions.toString() : "";
            }
            
            // 如果请求参数为空，尝试从请求对象中获取条件
            if (requestObj != null) {
                return getConditionsFromRequestObj(requestObj, entityClass);
            }
            
            return "";
        } catch (Exception e) {
            log.debug("获取导出条件失败", e);
            return "";
        }
    }

    /**
     * 从请求对象中获取条件
     */
    private String getConditionsFromRequestObj(Object requestObj, Class<?> entityClass) {
        try {
            StringJoiner conditions = new StringJoiner(", ", "，导出条件：", "");
            
            // 通过反射获取请求对象的非空字段
            java.lang.reflect.Field[] fields = requestObj.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(requestObj);
                
                if (value != null && StringUtils.isNotEmpty(value.toString()) && 
                    !isExcludeField(field.getName())) {
                    try {
                        String fieldContent = OperatorLogMetaDataBuilder.getColumnCommentAndDictValue(
                            entityClass, field.getName(), value.toString()
                        );
                        conditions.add(fieldContent);
                    } catch (Exception e) {
                        conditions.add(field.getName() + "=" + value);
                    }
                }
            }
            
            return conditions.length() > 0 ? conditions.toString() : "";
        } catch (Exception e) {
            log.debug("从请求对象获取导出条件失败", e);
            return "";
        }
    }

    /**
     * 判断是否为需要排除的字段
     */
    private boolean isExcludeField(String fieldName) {
        for (String excludeProperty : EXCLUDE_EXPORT_PROPERTIES) {
            if (excludeProperty.equals(fieldName)) {
                return true;
            }
        }
        return "serialVersionUID".equals(fieldName) || 
               "class".equals(fieldName);
    }
}
