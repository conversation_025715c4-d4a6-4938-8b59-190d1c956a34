package org.dromara.cssrcSoftware.domain.vo;

import org.dromara.cssrcSoftware.domain.SoftwareCategory;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 软件分类视图对象 software_category
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SoftwareCategory.class)
public class SoftwareCategoryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分类ID
     */
    @ExcelProperty(value = "分类ID")
    private Long categoryId;

    /**
     * 分类名称
     */
    @ExcelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 父级分类ID
     */
    @ExcelProperty(value = "父级分类ID")
    private Long parentId;

    /**
     * 祖级分类路径
     */
    @ExcelProperty(value = "祖级分类路径")
    private String ancestors;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 所属范围
     */
    @ExcelProperty(value = "所属范围")
    private String softwareRange;

    /**
     * 状态（0正常 1停用）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=正常,1=停用")
    private String status;


}
