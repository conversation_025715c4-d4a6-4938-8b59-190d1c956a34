package org.dromara.cssrcSoftware.domain.bo;

import org.dromara.cssrcSoftware.domain.SoftwareDownloadLog;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 软件下载日志业务对象 software_download_log
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SoftwareDownloadLog.class, reverseConvertGenerate = false)
public class SoftwareDownloadLogBo extends BaseEntity {

    /**
     * 日志ID
     */
    @NotNull(message = "日志ID不能为空", groups = { EditGroup.class })
    private Long logId;

    /**
     * 软件ID
     */
    @NotNull(message = "软件ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long softwareId;

    /**
     * 版本ID
     */
    @NotNull(message = "版本ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long versionId;

    /**
     * 下载用户ID
     */
    @NotNull(message = "下载用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 下载IP
     */
    private String ipAddress;

    /**
     * 下载时间
     */
    private Date downloadTime;

    /**
     * 下载状态
     */
    private String status;


}
