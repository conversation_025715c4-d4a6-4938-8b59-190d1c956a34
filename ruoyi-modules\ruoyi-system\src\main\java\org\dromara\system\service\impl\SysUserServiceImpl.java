package org.dromara.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.CacheNames;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.domain.dto.UserDTO;
import org.dromara.common.core.domain.event.ProcessDeleteEvent;
import org.dromara.common.core.domain.event.ProcessEvent;
import org.dromara.common.core.domain.event.ProcessTaskEvent;
import org.dromara.common.core.enums.BusinessStatusEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.service.UserService;
import org.dromara.common.core.service.WorkflowService;
import org.dromara.common.core.utils.*;
import org.dromara.common.encrypt.utils.EncryptUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.system.domain.*;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.domain.vo.SysPostVo;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.domain.vo.SysUserExportVo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.mapper.*;
import org.dromara.system.service.ISysDeptService;
import org.dromara.system.service.ISysUserService;
import org.dromara.system.service.ISysUserApprovalService;
import org.dromara.system.domain.vo.SysUserApprovalVo;
import org.dromara.common.json.utils.JsonUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 *
 * <AUTHOR> Li
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysUserServiceImpl implements ISysUserService, UserService {

    private final SysUserMapper baseMapper;
    private final SysDeptMapper deptMapper;
    private final SysRoleMapper roleMapper;
    private final SysPostMapper postMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final SysUserPostMapper userPostMapper;

    private final ISysUserApprovalService userApprovalService;
    private final ISysDeptService deptService;

    @Override
    public TableDataInfo<SysUserVo> selectPageUserList(SysUserBo user, PageQuery pageQuery) {
        Page<SysUserVo> page = baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(user));
        // // 手动解密
        // if (BooleanUtil.toBoolean(SpringUtils.getProperty("mybatis-encryptor.enable"))) {
        //     page.getRecords().forEach(record -> {
        //         record.setSecret(EncryptUtils.decryptByBase64(record.getSecret()));
        //     });
        // }
        return TableDataInfo.build(page);
    }

    @Override
    public TableDataInfo<SysUserVo> selectPageUserList(SysUserBo user, PageQuery pageQuery, String deptParam) {
        // 如果用户点击了左侧的树，则user是已经有了点击的deptId的，直接用就行
        if (user.getDeptId() == null) {
            // 根据层级筛选获取正确的部门
            Long parentId = deptService.getDefaultParent(deptParam);
            user.setDeptId(parentId);
        }

        Page<SysUserVo> page = baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(user));



        return TableDataInfo.build(page);
    }

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUserExportVo> selectUserExportList(SysUserBo user) {
        Map<String, Object> params = user.getParams();
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", SystemConstants.NORMAL)
            .like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName())
            .like(StringUtils.isNotBlank(user.getNickName()), "u.nick_name", user.getNickName())
            .eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus())
            .like(StringUtils.isNotBlank(user.getPhonenumber()), "u.phonenumber", user.getPhonenumber())
            .between(params.get("beginTime") != null && params.get("endTime") != null,
                "u.create_time", params.get("beginTime"), params.get("endTime"))
            .and(ObjectUtil.isNotNull(user.getDeptId()), w -> {
                List<SysDept> deptList = deptMapper.selectListByParentId(user.getDeptId());
                List<Long> ids = StreamUtils.toList(deptList, SysDept::getDeptId);
                ids.add(user.getDeptId());
                w.in("u.dept_id", ids);
            }).orderByAsc("u.user_id");

        // 添加密级字段的处理
        if (StringUtils.isNotBlank(user.getSecret())) {
            if (BooleanUtil.toBoolean(SpringUtils.getProperty("mybatis-encryptor.enable"))) {
                wrapper.eq("u.secret", EncryptUtils.encryptByBase64(user.getSecret()));
            } else {
                wrapper.eq("u.secret", user.getSecret());
            }
        }

        List<SysUserExportVo> list = baseMapper.selectUserExportList(wrapper);

        // 手动解密密级字段 - 关键修改点
        if (BooleanUtil.toBoolean(SpringUtils.getProperty("mybatis-encryptor.enable"))) {
            list.forEach(record -> {
                if (StringUtils.isNotBlank(record.getSecret())) {
                    try {
                        String decryptedSecret = EncryptUtils.decryptByBase64(record.getSecret());
                        record.setSecret(decryptedSecret);
                        log.debug("解密密级: {} -> {}", record.getSecret(), decryptedSecret);
                    } catch (Exception e) {
                        log.error("解密密级失败: {}", record.getSecret(), e);
                        // 解密失败时保持原值或设置为空
                        record.setSecret("");
                    }
                }
            });
        }

        return list;
    }



    private Wrapper<SysUser> buildQueryWrapper(SysUserBo user) {
        Map<String, Object> params = user.getParams();
        LambdaQueryWrapper<SysUser> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SysUser::getDelFlag, SystemConstants.NORMAL)
            .eq(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId())
            .in(StringUtils.isNotBlank(user.getUserIds()), SysUser::getUserId, StringUtils.splitTo(user.getUserIds(), Convert::toLong))
            .like(StringUtils.isNotBlank(user.getUserName()), SysUser::getUserName, user.getUserName())
            .like(StringUtils.isNotBlank(user.getNickName()), SysUser::getNickName, user.getNickName())
            .eq(StringUtils.isNotBlank(user.getStatus()), SysUser::getStatus, user.getStatus())
            .like(StringUtils.isNotBlank(user.getPhonenumber()), SysUser::getPhonenumber, user.getPhonenumber())
            .between(params.get("beginTime") != null && params.get("endTime") != null,
                SysUser::getCreateTime, params.get("beginTime"), params.get("endTime"))
            .and(ObjectUtil.isNotNull(user.getDeptId()), w -> {
                List<SysDept> deptList = deptMapper.selectListByParentId(user.getDeptId());
                List<Long> ids = StreamUtils.toList(deptList, SysDept::getDeptId);
                ids.add(user.getDeptId());
                w.in(SysUser::getDeptId, ids);
            }).orderByAsc(SysUser::getUserId);
        if (StringUtils.isNotBlank(user.getExcludeUserIds())) {
            wrapper.notIn(SysUser::getUserId, StringUtils.splitList(user.getExcludeUserIds()));
        }
        if (BooleanUtil.toBoolean(SpringUtils.getProperty("mybatis-encryptor.enable"))) { // 排除密级不符合的
            wrapper.eq(StringUtils.isNotBlank(user.getSecret()), SysUser::getSecret, EncryptUtils.encryptByBase64(user.getSecret()));
        }else {
            wrapper.eq(StringUtils.isNotBlank(user.getSecret()), SysUser::getSecret, user.getSecret());
        }

        return wrapper;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public TableDataInfo<SysUserVo> selectAllocatedList(SysUserBo user, PageQuery pageQuery) {
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", SystemConstants.NORMAL)
            .eq(ObjectUtil.isNotNull(user.getRoleId()), "r.role_id", user.getRoleId())
            .like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName())
            .like(StringUtils.isNotBlank(user.getNickName()), "u.nick_name", user.getNickName())
            .eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus())
            .like(StringUtils.isNotBlank(user.getPhonenumber()), "u.phonenumber", user.getPhonenumber())
            .orderByAsc("u.user_id");
        Page<SysUserVo> page = baseMapper.selectAllocatedList(pageQuery.build(), wrapper);
        return TableDataInfo.build(page);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public TableDataInfo<SysUserVo> selectUnallocatedList(SysUserBo user, PageQuery pageQuery) {
        List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(user.getRoleId());
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", SystemConstants.NORMAL)
            .and(w -> w.ne("r.role_id", user.getRoleId()).or().isNull("r.role_id"))
            .notIn(CollUtil.isNotEmpty(userIds), "u.user_id", userIds)
            .like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName())
            .like(StringUtils.isNotBlank(user.getNickName()), "u.nick_name", user.getNickName())
            .like(StringUtils.isNotBlank(user.getPhonenumber()), "u.phonenumber", user.getPhonenumber())
            .orderByAsc("u.user_id");
        Page<SysUserVo> page = baseMapper.selectUnallocatedList(pageQuery.build(), wrapper);
        return TableDataInfo.build(page);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUserVo selectUserByUserName(String userName) {
        return baseMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, userName));
    }

    /**
     * 通过手机号查询用户
     *
     * @param phonenumber 手机号
     * @return 用户对象信息
     */
    @Override
    public SysUserVo selectUserByPhonenumber(String phonenumber) {
        return baseMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getPhonenumber, phonenumber));
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUserVo selectUserById(Long userId) {
        SysUserVo user = baseMapper.selectVoById(userId);
        if (ObjectUtil.isNull(user)) {
            return user;
        }
        user.setRoles(roleMapper.selectRolesByUserId(user.getUserId()));
        return user;
    }

    /**
     * 通过用户ID查询用户（包括逻辑删除的用户）
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUserVo selectUserByIdIncludeDeleted(Long userId) {
        SysUserVo user = baseMapper.selectUserByIdIncludeDeleted(userId);
        if (ObjectUtil.isNull(user)) {
            return user;
        }
        user.setRoles(roleMapper.selectRolesByUserId(user.getUserId()));
        return user;
    }

    /**
     * 通过用户ID串查询用户
     *
     * @param userIds 用户ID串
     * @param deptId  部门id
     * @return 用户列表信息
     */
    @Override
    public List<SysUserVo> selectUserByIds(List<Long> userIds, Long deptId) {
        return baseMapper.selectUserList(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getUserId, SysUser::getUserName, SysUser::getNickName)
            .eq(SysUser::getStatus, SystemConstants.NORMAL)
            .eq(ObjectUtil.isNotNull(deptId), SysUser::getDeptId, deptId)
            .in(CollUtil.isNotEmpty(userIds), SysUser::getUserId, userIds));
    }

    /**
     * 查询用户所属角色组
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(Long userId) {
        List<SysRoleVo> list = roleMapper.selectRolesByUserId(userId);
        if (CollUtil.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return StreamUtils.join(list, SysRoleVo::getRoleName);
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(Long userId) {
        List<SysPostVo> list = postMapper.selectPostsByUserId(userId);
        if (CollUtil.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return StreamUtils.join(list, SysPostVo::getPostName);
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUserBo user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getUserName, user.getUserName())
            .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public boolean checkPhoneUnique(SysUserBo user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getPhonenumber, user.getPhonenumber())
            .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public boolean checkEmailUnique(SysUserBo user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getEmail, user.getEmail())
            .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param userId 用户ID
     */
    @Override
    public void checkUserAllowed(Long userId) {
        if (ObjectUtil.isNotNull(userId) && LoginHelper.isSuperAdmin(userId)) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (ObjectUtil.isNull(userId)) {
            return;
        }
        // 默认管理员有数据权限
        if (LoginHelper.isSuperAdmin() || LoginHelper.isRightAdmin() || LoginHelper.isSystemAdmin()) {
            return;
        }
        if (baseMapper.countUserById(userId) == 0) {
            throw new ServiceException("没有权限访问用户数据！");
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUserVo insertUser(SysUserBo user) {
        SysUser sysUser = MapstructUtils.convert(user, SysUser.class);
        // 新增用户信息
        boolean flag = baseMapper.insert(sysUser) >0;
        if (flag) {
            // 将数据库生成的用户ID设置回SysUserBo对象中
            user.setUserId(sysUser.getUserId());
            // 新增用户岗位关联
            insertUserPost(user, false);
            // 新增用户与角色管理
            insertUserRole(user, false);
        }


        return MapstructUtils.convert(sysUser, SysUserVo.class);
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUserBo user, String tenantId) {
        user.setCreateBy(0L);
        user.setUpdateBy(0L);
        SysUser sysUser = MapstructUtils.convert(user, SysUser.class);
        sysUser.setTenantId(tenantId);
        return baseMapper.insert(sysUser) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @CacheEvict(cacheNames = CacheNames.SYS_NICKNAME, key = "#user.userId")
    @Transactional(rollbackFor = Exception.class)
    public SysUserVo updateUser(SysUserBo user) {
        // 新增用户与角色管理
        insertUserRole(user, true);
        // 新增用户与岗位管理
        insertUserPost(user, true);
        SysUser sysUser = MapstructUtils.convert(user, SysUser.class);
        // 防止错误更新后导致的数据误删除
        int flag = baseMapper.updateById(sysUser);
        if (flag < 1) {
            throw new ServiceException("修改用户" + user.getUserName() + "信息失败");
        }
        return MapstructUtils.convert(sysUser, SysUserVo.class);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(Long userId, Long[] roleIds) {
        insertUserRole(userId, roleIds, true);
    }

    /**
     * 修改用户状态
     *
     * @param userId 用户ID
     * @param status 帐号状态
     * @return 结果
     */
    @Override
    public int updateUserStatus(Long userId, String status) {
        return baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(SysUser::getStatus, status)
                .eq(SysUser::getUserId, userId));
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @CacheEvict(cacheNames = CacheNames.SYS_NICKNAME, key = "#user.userId")
    @Override
    public int updateUserProfile(SysUserBo user) {
        return baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(ObjectUtil.isNotNull(user.getNickName()), SysUser::getNickName, user.getNickName())
                .set(SysUser::getPhonenumber, user.getPhonenumber())
                .set(SysUser::getEmail, user.getEmail())
                .set(SysUser::getSex, user.getSex())
                .eq(SysUser::getUserId, user.getUserId()));
    }

    /**
     * 修改用户头像
     *
     * @param userId 用户ID
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(Long userId, Long avatar) {
        return baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(SysUser::getAvatar, avatar)
                .eq(SysUser::getUserId, userId)) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param userId   用户ID
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(Long userId, String password) {
        return baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(SysUser::getPassword, password)
                .eq(SysUser::getUserId, userId));
    }

    /**
     * 新增用户角色信息
     *
     * @param user  用户对象
     * @param clear 清除已存在的关联数据
     */
    private void insertUserRole(SysUserBo user, boolean clear) {
        this.insertUserRole(user.getUserId(), user.getRoleIds(), clear);
    }

    /**
     * 新增用户岗位信息
     *
     * @param user  用户对象
     * @param clear 清除已存在的关联数据
     */
    private void insertUserPost(SysUserBo user, boolean clear) {
        Long[] posts = user.getPostIds();
        if (ArrayUtil.isNotEmpty(posts)) {
            if (clear) {
                // 删除用户与岗位关联
                userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, user.getUserId()));
            }
            // 新增用户与岗位管理
            List<SysUserPost> list = StreamUtils.toList(List.of(posts), postId -> {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                return up;
            });
            userPostMapper.insertBatch(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     * @param clear   清除已存在的关联数据
     */
    private void insertUserRole(Long userId, Long[] roleIds, boolean clear) {
        if (ArrayUtil.isNotEmpty(roleIds)) {
            List<Long> roleList = new ArrayList<>(List.of(roleIds));
            if (!LoginHelper.isSuperAdmin(userId)) {
                roleList.remove(SystemConstants.SUPER_ADMIN_ID);
            }
            // 判断是否具有此角色的操作权限
            List<SysRoleVo> roles = roleMapper.selectRoleList(
                new QueryWrapper<SysRole>().in("r.role_id", roleList));
            if (CollUtil.isEmpty(roles)) {
                throw new ServiceException("没有权限访问角色的数据");
            }
            if (clear) {
                // 删除用户与角色关联
                userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
            }
            // 新增用户与角色管理
            List<SysUserRole> list = StreamUtils.toList(roleList, roleId -> {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                return ur;
            });
            userRoleMapper.insertBatch(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        // 删除用户与岗位表
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, userId));
        // 防止更新失败导致的数据删除
        int flag = baseMapper.deleteById(userId);
        if (flag < 1) {
            throw new ServiceException("删除用户失败!");
        }
        return flag;
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(userId);
            checkUserDataScope(userId);
        }
        List<Long> ids = List.of(userIds);
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getUserId, ids));
        // 删除用户与岗位表
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().in(SysUserPost::getUserId, ids));
        // 防止更新失败导致的数据删除
        int flag = baseMapper.deleteByIds(ids);
        if (flag < 1) {
            throw new ServiceException("删除用户失败!");
        }
        return flag;
    }

    /**
     * 通过部门id查询当前部门所有用户
     *
     * @param deptId 部门ID
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUserVo> selectUserListByDept(Long deptId) {
        LambdaQueryWrapper<SysUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysUser::getDeptId, deptId);
        lqw.orderByAsc(SysUser::getUserId);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 通过用户ID查询用户账户
     *
     * @param userId 用户ID
     * @return 用户账户
     */
    @Cacheable(cacheNames = CacheNames.SYS_USER_NAME, key = "#userId")
    @Override
    public String selectUserNameById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getUserName).eq(SysUser::getUserId, userId));
        return ObjectUtils.notNullGetter(sysUser, SysUser::getUserName);
    }

    /**
     * 通过用户ID查询用户账户
     *
     * @param userId 用户ID
     * @return 用户账户
     */
    @Override
    @Cacheable(cacheNames = CacheNames.SYS_NICKNAME, key = "#userId")
    public String selectNicknameById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getNickName).eq(SysUser::getUserId, userId));
        return ObjectUtils.notNullGetter(sysUser, SysUser::getNickName);
    }

    /**
     * 通过用户ID查询用户部门ID
     *
     * @param userId 用户ID
     * @return 用户部门ID
     */
//    @Cacheable(cacheNames = CacheNames.SYS_DEPT, key = "#userId")
    @Override
    public Long selectDeptIdById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getDeptId).eq(SysUser::getUserId, userId));
        return ObjectUtils.notNullGetter(sysUser, SysUser::getDeptId);
    }

    /**
     * 通过用户ID查询用户账户
     *
     * @param userIds 用户ID 多个用逗号隔开
     * @return 用户账户
     */
    @Override
    public String selectNicknameByIds(String userIds) {
        List<String> list = new ArrayList<>();
        for (Long id : StringUtils.splitTo(userIds, Convert::toLong)) {
            String nickname = SpringUtils.getAopProxy(this).selectNicknameById(id);
            if (StringUtils.isNotBlank(nickname)) {
                list.add(nickname);
            }
        }
        return String.join(StringUtils.SEPARATOR, list);
    }

    /**
     * 通过用户ID查询用户手机号
     *
     * @param userId 用户id
     * @return 用户手机号
     */
    @Override
    public String selectPhonenumberById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getPhonenumber).eq(SysUser::getUserId, userId));
        return ObjectUtils.notNullGetter(sysUser, SysUser::getPhonenumber);
    }

    /**
     * 通过用户ID查询用户邮箱
     *
     * @param userId 用户id
     * @return 用户邮箱
     */
    @Override
    public String selectEmailById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getEmail).eq(SysUser::getUserId, userId));
        return ObjectUtils.notNullGetter(sysUser, SysUser::getEmail);
    }

    /**
     * 通过用户ID查询用户列表
     *
     * @param userIds 用户ids
     * @return 用户列表
     */
    @Override
    public List<UserDTO> selectListByIds(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return List.of();
        }
        List<SysUserVo> list = baseMapper.selectVoList(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getUserId, SysUser::getDeptId, SysUser::getUserName,
                SysUser::getNickName, SysUser::getUserType, SysUser::getEmail,
                SysUser::getPhonenumber, SysUser::getSex, SysUser::getStatus,
                SysUser::getCreateTime)
            .eq(SysUser::getStatus, SystemConstants.NORMAL)
            .in(SysUser::getUserId, userIds));
        return BeanUtil.copyToList(list, UserDTO.class);
    }

    /**
     * 通过角色ID查询用户ID
     *
     * @param roleIds 角色ids
     * @return 用户ids
     */
    @Override
    public List<Long> selectUserIdsByRoleIds(List<Long> roleIds) {
        if (CollUtil.isEmpty(roleIds)) {
            return List.of();
        }
        List<SysUserRole> userRoles = userRoleMapper.selectList(
            new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getRoleId, roleIds));
        return StreamUtils.toList(userRoles, SysUserRole::getUserId);
    }

    /**
     * 通过角色ID查询用户
     *
     * @param roleIds 角色ids
     * @return 用户
     */
    @Override
    public List<UserDTO> selectUsersByRoleIds(List<Long> roleIds) {
        if (CollUtil.isEmpty(roleIds)) {
            return List.of();
        }

        // 通过角色ID获取用户角色信息
        List<SysUserRole> userRoles = userRoleMapper.selectList(
            new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getRoleId, roleIds));

        // 获取用户ID列表
        Set<Long> userIds = StreamUtils.toSet(userRoles, SysUserRole::getUserId);

        return this.selectListByIds(new ArrayList<>(userIds));
    }

    /**
     * 通过部门ID查询用户
     *
     * @param deptIds 部门ids
     * @return 用户
     */
    @Override
    public List<UserDTO> selectUsersByDeptIds(List<Long> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            return List.of();
        }
        List<SysUserVo> list = baseMapper.selectVoList(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getUserId, SysUser::getUserName, SysUser::getNickName, SysUser::getEmail, SysUser::getPhonenumber)
            .eq(SysUser::getStatus, SystemConstants.NORMAL)
            .in(SysUser::getDeptId, deptIds));
        return BeanUtil.copyToList(list, UserDTO.class);
    }

    /**
     * 通过部门ID查询用户ID
     *
     * @param deptIds 部门ids
     * @return 用户ids
     */
    @Override
    public List<Long> selectUserIdsByDeptIds(List<Long> deptIds) {
        return StreamUtils.toList(selectUsersByDeptIds(deptIds), UserDTO::getUserId);
    }

    /**
     * 通过岗位ID查询用户
     *
     * @param postIds 岗位ids
     * @return 用户
     */
    @Override
    public List<UserDTO> selectUsersByPostIds(List<Long> postIds) {
        if (CollUtil.isEmpty(postIds)) {
            return List.of();
        }

        // 通过岗位ID获取用户岗位信息
        List<SysUserPost> userPosts = userPostMapper.selectList(
            new LambdaQueryWrapper<SysUserPost>().in(SysUserPost::getPostId, postIds));

        // 获取用户ID列表
        Set<Long> userIds = StreamUtils.toSet(userPosts, SysUserPost::getUserId);

        return this.selectListByIds(new ArrayList<>(userIds));
    }

    /**
     * 根据用户 ID 列表查询用户名称映射关系
     *
     * @param userIds 用户 ID 列表
     * @return Map，其中 key 为用户 ID，value 为对应的用户名称
     */
    @Override
    public Map<Long, String> selectUserNamesByIds(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        return baseMapper.selectList(
                new LambdaQueryWrapper<SysUser>()
                    .select(SysUser::getUserId, SysUser::getNickName)
                    .in(SysUser::getUserId, userIds)
            ).stream()
            .collect(Collectors.toMap(SysUser::getUserId, SysUser::getNickName));
    }

    /**
     * 根据角色 ID 列表查询角色名称映射关系
     *
     * @param roleIds 角色 ID 列表
     * @return Map，其中 key 为角色 ID，value 为对应的角色名称
     */
    @Override
    public Map<Long, String> selectRoleNamesByIds(List<Long> roleIds) {
        if (CollUtil.isEmpty(roleIds)) {
            return Collections.emptyMap();
        }
        return roleMapper.selectList(
                new LambdaQueryWrapper<SysRole>()
                    .select(SysRole::getRoleId, SysRole::getRoleName)
                    .in(SysRole::getRoleId, roleIds)
            ).stream()
            .collect(Collectors.toMap(SysRole::getRoleId, SysRole::getRoleName));
    }

    /**
     * 根据部门 ID 列表查询部门名称映射关系
     *
     * @param deptIds 部门 ID 列表
     * @return Map，其中 key 为部门 ID，value 为对应的部门名称
     */
    @Override
    public Map<Long, String> selectDeptNamesByIds(List<Long> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            return Collections.emptyMap();
        }
        return deptMapper.selectList(
                new LambdaQueryWrapper<SysDept>()
                    .select(SysDept::getDeptId, SysDept::getDeptName)
                    .in(SysDept::getDeptId, deptIds)
            ).stream()
            .collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
    }

    /**
     * 根据岗位 ID 列表查询岗位名称映射关系
     *
     * @param postIds 岗位 ID 列表
     * @return Map，其中 key 为岗位 ID，value 为对应的岗位名称
     */
    @Override
    public Map<Long, String> selectPostNamesByIds(List<Long> postIds) {
        if (CollUtil.isEmpty(postIds)) {
            return Collections.emptyMap();
        }
        return postMapper.selectList(
                new LambdaQueryWrapper<SysPost>()
                    .select(SysPost::getPostId, SysPost::getPostName)
                    .in(SysPost::getPostId, postIds)
            ).stream()
            .collect(Collectors.toMap(SysPost::getPostId, SysPost::getPostName));
    }

    /**
     * 重置用户流程状态，允许重新发起流程
     *
     * @param userId 用户ID
     */
    @Override
    public void resetUserFlowStatus(Long userId) {
        baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(SysUser::getFlowStatus, BusinessStatusEnum.DRAFT.getStatus())
                .eq(SysUser::getUserId, userId));
        log.info("重置用户流程状态为草稿，用户ID: {}", userId);
    }

    /**
     * 用户信息修改流程监听
     * 当流程审批通过后，应用用户信息变更
     *
     * @param processEvent 流程事件
     */
    @EventListener(condition = "#processEvent.flowCode.equals('userUpdate')")
    @Transactional(rollbackFor = Exception.class)
    public void processHandler(ProcessEvent processEvent) {
        log.info("用户信息修改流程执行: {}", processEvent.toString());

        // 获取业务ID（审批记录ID）
        String businessId = processEvent.getBusinessId();
        if (StringUtils.isBlank(businessId)) {
            log.warn("用户信息修改流程缺少业务ID");
            return;
        }

        // 根据审批记录ID获取用户ID
        Long approvalId = Long.valueOf(businessId);
        Long userId = userApprovalService.getUserIdByApprovalId(approvalId);
        if (userId == null) {
            log.warn("无法根据审批ID获取用户ID: {}", approvalId);
            return;
        }

        // 获取流程变量中的用户变更信息
        Map<String, Object> params = processEvent.getParams();
        if (ObjectUtil.isEmpty(params)) {
            log.warn("用户信息修改流程缺少参数");
            return;
        }

        // 获取操作类型（新增或修改）
        String operationType = (String) params.get("operationType");
        if (StringUtils.isBlank(operationType)) {
            log.warn("用户信息流程缺少操作类型");
            return;
        }

        // 如果是提交操作，对于修改操作更新用户流程状态为待审核
        if (processEvent.getSubmit()) {
            // 对于新增操作，已经在Controller中设置为待审核状态
            if (("edit".equals(operationType) || "delete".equals(operationType)) && userId != null) {
                // 对于修改和删除操作，更新用户流程状态为待审核
                baseMapper.update(null,
                    new LambdaUpdateWrapper<SysUser>()
                        .set(SysUser::getFlowStatus, BusinessStatusEnum.WAITING.getStatus())
                        .eq(SysUser::getUserId, userId));
            }
            // 更新审批记录状态为待审核
            userApprovalService.updateApprovalStatus(approvalId, BusinessStatusEnum.WAITING.getStatus(), null);
        }

        // 如果流程完成，应用用户变更
        if (BusinessStatusEnum.FINISH.getStatus().equals(processEvent.getStatus())) {
            if ("add".equals(operationType)) {
                // 新增操作 - 审批通过，创建用户
                handleUserAddApproval(approvalId, params);
            } else if ("edit".equals(operationType)) {
                // 修改操作 - 审批通过，应用用户信息变更
                handleUserEditApproval(approvalId, userId, params);
            } else if ("delete".equals(operationType)) {
                // 删除操作 - 审批通过，停用用户
                handleUserDeleteApproval(approvalId, userId);
            }
        } else if (BusinessStatusEnum.INVALID.getStatus().equals(processEvent.getStatus()) ||
            BusinessStatusEnum.CANCEL.getStatus().equals(processEvent.getStatus()) ||
            BusinessStatusEnum.TERMINATION.getStatus().equals(processEvent.getStatus())) {
            // 如果流程被拒绝、取消或终止，处理审批失败
            handleApprovalRejected(approvalId, operationType, userId);
        }
//        else {
//            // 其他状态处理（如草稿等）
//            Long userId = Long.valueOf(businessId);
//            baseMapper.update(null,
//                new LambdaUpdateWrapper<SysUser>()
//                    .set(SysUser::getFlowStatus, processEvent.getStatus())
//                    .eq(SysUser::getUserId, userId));
//        }
    }

    /**
     * 用户信息流程任务创建监听
     * 当流程任务创建时更新用户流程状态
     *
     * @param processTaskEvent 流程任务事件
     */
    @EventListener(condition = "#processTaskEvent.flowCode.equals('userUpdate')")
    public void processTaskHandler(ProcessTaskEvent processTaskEvent) {
        log.info("用户信息流程任务创建: {}", processTaskEvent.toString());

        String businessId = processTaskEvent.getBusinessId();
        if (StringUtils.isBlank(businessId)) {
            return;
        }

        // 如果是结束节点的任务事件，不更新流程状态，避免覆盖已完成的状态
        if (processTaskEvent.getNodeType() != null && processTaskEvent.getNodeType() == 2) {
            log.info("结束节点任务事件，跳过状态更新，审批ID: {}", businessId);
            return;
        }

        // 根据审批记录ID获取用户ID
        Long approvalId = Long.valueOf(businessId);
        Long userId = userApprovalService.getUserIdByApprovalId(approvalId);

        // 更新用户流程状态（如果用户存在）
        if (userId != null) {
            baseMapper.update(null,
                new LambdaUpdateWrapper<SysUser>()
                    .set(SysUser::getFlowStatus, processTaskEvent.getStatus())
                    .eq(SysUser::getUserId, userId));
        }

        // 更新审批记录状态
        userApprovalService.updateApprovalStatus(approvalId, processTaskEvent.getStatus(), null);
    }

    /**
     * 用户信息流程删除监听
     * 当流程被删除时的处理
     *
     * @param processDeleteEvent 流程删除事件
     */
    @EventListener(condition = "#processDeleteEvent.flowCode.equals('userUpdate')")
    public void processDeleteHandler(ProcessDeleteEvent processDeleteEvent) {
        log.info("用户信息流程删除: {}", processDeleteEvent.toString());

        String businessId = processDeleteEvent.getBusinessId();
        if (StringUtils.isBlank(businessId)) {
            return;
        }

        // 根据审批记录ID获取用户ID
        Long approvalId = Long.valueOf(businessId);
        Long userId = userApprovalService.getUserIdByApprovalId(approvalId);

        if (userId != null) {
            // 更新用户流程状态为作废
            baseMapper.update(null,
                new LambdaUpdateWrapper<SysUser>()
                    .set(SysUser::getFlowStatus, BusinessStatusEnum.INVALID.getStatus())
                    .eq(SysUser::getUserId, userId));
        }

        // 更新审批记录状态为作废
        userApprovalService.updateApprovalStatus(approvalId, BusinessStatusEnum.INVALID.getStatus(), null);
        log.info("用户信息流程删除处理完成，审批ID: {}, 用户ID: {}", approvalId, userId);
    }

    /**
     * 计算用户信息差异（包括用户基本信息、角色和岗位）
     *
     * @param originalUser 原始用户信息
     * @param modifiedUser 修改后的用户信息
     * @return 差异信息
     */
    public Map<String, Object> computeDiff(SysUserVo originalUser, SysUserBo modifiedUser) {
        Map<String, Object> diff = new HashMap<>();

        // 计算用户基本信息差异
        Map<String, Object> originalMap = BeanUtil.beanToMap(originalUser);
        Map<String, Object> modifiedMap = BeanUtil.beanToMap(modifiedUser);

        for (Map.Entry<String, Object> entry : modifiedMap.entrySet()) {
            String key = entry.getKey();
//            if ("password".equals(key)) {
//                continue;
//            }
            Object modifiedValue = entry.getValue();
            Object originalValue = originalMap.get(key);

            // 比较值是否发生变化
            if (!Objects.equals(originalValue, modifiedValue)) {
                diff.put(key, modifiedValue);
            }
        }

        // 计算角色差异
        Long[] originalRoleIds = originalUser.getRoles().stream()
            .map(SysRoleVo::getRoleId)
            .toArray(Long[]::new);
        Long[] modifiedRoleIds = modifiedUser.getRoleIds();

        if (!Arrays.equals(originalRoleIds, modifiedRoleIds)) {
            diff.put("roleIds", modifiedRoleIds);
        }

        // 计算岗位差异
        // 获取原始岗位信息
        List<SysPostVo> originalPosts = postMapper.selectPostsByUserId(originalUser.getUserId());
        Long[] originalPostIds = originalPosts.stream()
            .map(SysPostVo::getPostId)
            .toArray(Long[]::new);
        Long[] modifiedPostIds = modifiedUser.getPostIds();

        if (!Arrays.equals(originalPostIds, modifiedPostIds)) {
            diff.put("postIds", modifiedPostIds);
        }

        return diff;
    }

    /**
     * 应用差异到用户信息（包括用户基本信息、角色和岗位）
     *
     * @param user 用户信息
     * @param diff 差异信息
     * @return 应用差异后的用户信息
     */
    private SysUserBo applyDiff(SysUserVo user, Map<String, Object> diff) {
        Map<String, Object> userMap = BeanUtil.beanToMap(user);
        userMap.putAll(diff);
        SysUserBo userBo = BeanUtil.toBean(userMap, SysUserBo.class);

        // 处理角色差异
        if (diff.containsKey("roleIds")) {
            userBo.setRoleIds((Long[]) diff.get("roleIds"));
        }

        // 处理岗位差异
        if (diff.containsKey("postIds")) {
            userBo.setPostIds((Long[]) diff.get("postIds"));
        }

        return userBo;
    }

    /**
     * 处理用户新增审批通过
     *
     * @param approvalId 审批ID
     * @param params 流程参数
     */
    private void handleUserAddApproval(Long approvalId, Map<String, Object> params) {
        try {
            // 获取审批记录
            SysUserApprovalVo approval = userApprovalService.queryById(approvalId);
            if (approval == null) {
                log.error("审批记录不存在，审批ID: {}", approvalId);
                return;
            }

            // 解析用户数据
            String userData = (String) params.get("userData");
            if (StringUtils.isBlank(userData)) {
                userData = approval.getApprovalData();
            }

            SysUserBo userBo = JsonUtils.parseObject(userData, SysUserBo.class);
            if (userBo == null) {
                log.error("用户数据解析失败，审批ID: {}", approvalId);
                return;
            }

            // 设置用户状态
            userBo.setStatus(SystemConstants.NORMAL);
            userBo.setDelFlag("0");
            userBo.setFlowStatus(BusinessStatusEnum.FINISH.getStatus());

            // 创建用户
            SysUserVo newUser = insertUser(userBo);
            if (newUser != null && newUser.getUserId() != null) {
                // 更新审批记录的用户ID
                userApprovalService.updateApprovalStatus(approvalId, BusinessStatusEnum.FINISH.getStatus(), null);
                log.info("新增用户审批通过，用户ID: {}", newUser.getUserId());
            } else {
                log.error("创建用户失败，审批ID: {}", approvalId);
            }
        } catch (Exception e) {
            log.error("处理用户新增审批失败，审批ID: {}", approvalId, e);
        }
    }

    /**
     * 处理用户修改审批通过
     *
     * @param approvalId 审批ID
     * @param userId 用户ID
     * @param params 流程参数
     */
    private void handleUserEditApproval(Long approvalId, Long userId, Map<String, Object> params) {
        try {
            // 获取审批记录
            SysUserApprovalVo approval = userApprovalService.queryById(approvalId);
            if (approval == null) {
                log.error("审批记录不存在，审批ID: {}", approvalId);
                return;
            }

            // 解析用户变更数据
            @SuppressWarnings("unchecked")
            Map<String, Object> userInfo = (Map<String, Object>) params.get("userInfo");
            if (userInfo == null || userInfo.isEmpty()) {
                log.warn("用户变更信息为空，审批ID: {}", approvalId);
                return;
            }

            // 获取原始用户信息
            SysUserVo originalUser = selectUserById(userId);
            if (originalUser == null) {
                log.error("用户不存在，用户ID: {}", userId);
                return;
            }

            // 应用变更
            SysUserBo updatedUser = applyDiff(originalUser, userInfo);
            updatedUser.setFlowStatus(BusinessStatusEnum.FINISH.getStatus());

            // 更新用户信息
            updateUser(updatedUser);

            // 更新审批记录状态
            userApprovalService.updateApprovalStatus(approvalId, BusinessStatusEnum.FINISH.getStatus(), null);
            log.info("用户修改审批通过，用户ID: {}", userId);
        } catch (Exception e) {
            log.error("处理用户修改审批失败，审批ID: {}, 用户ID: {}", approvalId, userId, e);
        }
    }

    /**
     * 处理用户删除（停用）审批通过
     *
     * @param approvalId 审批ID
     * @param userId 用户ID
     */
    private void handleUserDeleteApproval(Long approvalId, Long userId) {
        try {
            // 停用用户
            int result = baseMapper.update(null,
                new LambdaUpdateWrapper<SysUser>()
                    .set(SysUser::getStatus, SystemConstants.DISABLE) // 停用用户
                    .set(SysUser::getFlowStatus, BusinessStatusEnum.FINISH.getStatus()) // 流程状态完成
                    .eq(SysUser::getUserId, userId));

            if (result > 0) {
                // 更新审批记录状态
                userApprovalService.updateApprovalStatus(approvalId, BusinessStatusEnum.FINISH.getStatus(), null);
                log.info("用户停用审批通过，用户ID: {}", userId);
            } else {
                log.error("停用用户失败，用户ID: {}", userId);
            }
        } catch (Exception e) {
            log.error("处理用户停用审批失败，审批ID: {}, 用户ID: {}", approvalId, userId, e);
        }
    }

    /**
     * 处理审批被拒绝、取消或终止
     *
     * @param approvalId 审批ID
     * @param operationType 操作类型
     * @param userId 用户ID
     */
    private void handleApprovalRejected(Long approvalId, String operationType, Long userId) {
        try {
            if ("add".equals(operationType)) {
                // 新增操作被拒绝，无需处理（用户尚未创建）
                log.info("新增用户审批被拒绝，审批ID: {}", approvalId);
            } else if ("edit".equals(operationType) || "delete".equals(operationType)) {
                // 修改或删除操作被拒绝，恢复用户流程状态为正常
                if (userId != null) {
                    baseMapper.update(null,
                        new LambdaUpdateWrapper<SysUser>()
                            .set(SysUser::getFlowStatus, BusinessStatusEnum.FINISH.getStatus())
                            .eq(SysUser::getUserId, userId));
                    log.info("用户{}审批被拒绝，恢复用户流程状态，用户ID: {}", operationType, userId);
                }
            }

            // 更新审批记录状态
            userApprovalService.updateApprovalStatus(approvalId, BusinessStatusEnum.INVALID.getStatus(), null);
        } catch (Exception e) {
            log.error("处理审批拒绝失败，审批ID: {}, 操作类型: {}, 用户ID: {}", approvalId, operationType, userId, e);
        }
    }

}
