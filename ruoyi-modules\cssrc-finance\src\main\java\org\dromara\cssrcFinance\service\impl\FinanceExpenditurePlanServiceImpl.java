package org.dromara.cssrcFinance.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.event.ProcessTaskEvent;
import org.dromara.common.core.domain.event.ProcessDeleteEvent;
import org.dromara.common.core.domain.event.ProcessEvent;
import org.dromara.common.core.enums.BusinessStatusEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.system.domain.SysDept;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.mapper.SysDeptMapper;
import org.dromara.system.mapper.SysUserMapper;
import org.dromara.system.service.ISysDeptService;
import org.dromara.system.service.ISysUserService;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.dromara.cssrcFinance.domain.bo.FinanceExpenditurePlanBo;
import org.dromara.cssrcFinance.domain.vo.FinanceExpenditurePlanVo;
import org.dromara.cssrcFinance.domain.FinanceExpenditurePlan;
import org.dromara.cssrcFinance.mapper.FinanceExpenditurePlanMapper;
import org.dromara.cssrcFinance.service.IFinanceExpenditurePlanService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 财务-预算-支出计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class FinanceExpenditurePlanServiceImpl implements IFinanceExpenditurePlanService {

    private final FinanceExpenditurePlanMapper baseMapper;
    private final SysDeptMapper deptMapper;
    private final SysUserMapper userMapper;
    private final ISysDeptService deptService;
    private final ISysUserService userService;

    /**
     * 查询财务-预算-支出计划
     *
     * @param id 主键
     * @return 财务-预算-支出计划
     */
    @Override
    public FinanceExpenditurePlanVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询财务-预算-支出计划列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 财务-预算-支出计划分页列表
     */
    @Override
    public TableDataInfo<FinanceExpenditurePlanVo> queryPageList(FinanceExpenditurePlanBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinanceExpenditurePlan> lqw = buildQueryWrapper(bo);
        Page<FinanceExpenditurePlanVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的财务-预算-支出计划列表
     *
     * @param bo 查询条件
     * @return 财务-预算-支出计划列表
     */
    @Override
    public List<FinanceExpenditurePlanVo> queryList(FinanceExpenditurePlanBo bo) {
        LambdaQueryWrapper<FinanceExpenditurePlan> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinanceExpenditurePlan> buildQueryWrapper(FinanceExpenditurePlanBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinanceExpenditurePlan> lqw = Wrappers.lambdaQuery();

        // 创建字段映射表
        Map<String, SFunction<FinanceExpenditurePlan, ?>> orderColumns = new HashMap<>();
        orderColumns.put("nickName", FinanceExpenditurePlan::getUserId);
        orderColumns.put("subjectNumber", FinanceExpenditurePlan::getSubjectNumber);
        orderColumns.put("paymentTime", FinanceExpenditurePlan::getPaymentTime);
        // 根据传入排序顺序进行排序
        String orderColumn = bo.getOrderColumn();
        String orderType = bo.getOrderType();
        if (StringUtils.isNotBlank(orderColumn) && StringUtils.isNotBlank(orderType)) {
            SFunction<FinanceExpenditurePlan, ?> column = orderColumns.get(orderColumn);
            if (column != null) {
                if ("asc".equals(orderType)) {
                    lqw.orderByAsc(column);
                } else {
                    lqw.orderByDesc(column);
                }
            }
        } else {
            // 默认排序逻辑（无论是否匹配到字段都执行）
            lqw.orderByDesc(FinanceExpenditurePlan::getPaymentTime);
        }

        // 部门名称模糊搜索
        if (StringUtils.isNotBlank(bo.getDeptName())) {
            LambdaQueryWrapper<SysDept> deptQuery = new LambdaQueryWrapper<SysDept>()
                .like(SysDept::getDeptName, bo.getDeptName());
            List<SysDeptVo> sysDepts = deptMapper.selectDeptList(deptQuery);
            List<Long> deptIds = sysDepts.stream()
                .map(SysDeptVo::getDeptId)
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deptIds)) {
                lqw.in(FinanceExpenditurePlan::getDeptId, deptIds);
            }
        }
        // 申请人名称模糊搜索
        if (StringUtils.isNotBlank(bo.getNickName())) {
            LambdaQueryWrapper<SysUser> userQuery = new LambdaQueryWrapper<SysUser>()
                .like(SysUser::getNickName, bo.getNickName());
            List<SysUserVo> sysUsers = userMapper.selectUserList(userQuery);
            List<Long> userIds = sysUsers.stream()
                .map(SysUserVo::getUserId)
                .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(userIds)) {
                lqw.in(FinanceExpenditurePlan::getUserId, userIds);
            }
        }
        lqw.eq(StringUtils.isNotBlank(bo.getExpenditureType()), FinanceExpenditurePlan::getExpenditureType, bo.getExpenditureType());
        lqw.likeLeft(StringUtils.isNotBlank(bo.getSubjectNumber()), FinanceExpenditurePlan::getSubjectNumber, bo.getSubjectNumber());
        lqw.like(StringUtils.isNotBlank(bo.getSubjectName()), FinanceExpenditurePlan::getSubjectName, bo.getSubjectName());
        lqw.eq(StringUtils.isNotBlank(bo.getProjectType()), FinanceExpenditurePlan::getProjectType, bo.getProjectType());
        lqw.between(params.get("beginPaymentTime") != null && params.get("endPaymentTime") != null,
            FinanceExpenditurePlan::getPaymentTime ,params.get("beginPaymentTime"), params.get("endPaymentTime"));
        lqw.eq(StringUtils.isNotBlank(bo.getIsNobalanceProject()), FinanceExpenditurePlan::getIsNobalanceProject, bo.getIsNobalanceProject());
        lqw.eq(StringUtils.isNotBlank(bo.getFlowStatus()), FinanceExpenditurePlan::getFlowStatus, bo.getFlowStatus());
        return lqw;
    }

    /**
     * 新增财务-预算-支出计划
     *
     * @param bo 财务-预算-支出计划
     * @return 是否新增成功
     */
    @Override
    public FinanceExpenditurePlanVo insertByBo(FinanceExpenditurePlanBo bo) {
        FinanceExpenditurePlan add = MapstructUtils.convert(bo, FinanceExpenditurePlan.class);
        // 添加流程状态(草稿)
        if (StringUtils.isBlank(add.getFlowStatus())) {
            add.setFlowStatus(BusinessStatusEnum.DRAFT.getStatus());
        }
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            add.setId(add.getId());
        }
        return MapstructUtils.convert(add, FinanceExpenditurePlanVo.class);
    }

    /**
     * 修改财务-预算-支出计划
     *
     * @param bo 财务-预算-支出计划
     * @return 是否修改成功
     */
    @Override
    public FinanceExpenditurePlanVo updateByBo(FinanceExpenditurePlanBo bo) {
        FinanceExpenditurePlan update = MapstructUtils.convert(bo, FinanceExpenditurePlan.class);
        validEntityBeforeSave(update);
        int flag = baseMapper.updateById(update);
        if (flag < 1) {
            throw new ServiceException("支出计划" + update.getId() + "失败");
        }
        return MapstructUtils.convert(update, FinanceExpenditurePlanVo.class);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinanceExpenditurePlan entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除财务-预算-支出计划信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 处理导出到excel的数据
     *
     * @param financeExpenditurePlanList
     */
    public void handleExportExcelData(List<FinanceExpenditurePlanVo> financeExpenditurePlanList) {
        // 提取所有的dept_id并批量查询部门信息
        Set<Long> deptIds = financeExpenditurePlanList.stream()
            .map(FinanceExpenditurePlanVo::getDeptId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        List<SysDept> deptList = deptMapper.selectByIds(deptIds);
        Map<Long, SysDept> deptMap = deptList.stream()
            .collect(Collectors.toMap(SysDept::getDeptId, d -> d));

        // 提取所有user_id并批量查询用户信息
        Set<Long> userIds = financeExpenditurePlanList.stream()
            .map(FinanceExpenditurePlanVo::getUserId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        List<SysUser> userList = userMapper.selectByIds(userIds);
        Map<Long, SysUser> userMap = userList.stream()
            .collect(Collectors.toMap(SysUser::getUserId, d -> d));

        // 填充数据
        financeExpenditurePlanList = financeExpenditurePlanList.stream().map(financeExpenditurePlanVo -> {
            // 填充部门名称
            financeExpenditurePlanVo.setDeptName(deptMap.get(financeExpenditurePlanVo.getDeptId()).getDeptName());
            // 填充工号和姓名
            financeExpenditurePlanVo.setUserName(userMap.get(financeExpenditurePlanVo.getUserId()).getUserName());
            financeExpenditurePlanVo.setNickName(userMap.get(financeExpenditurePlanVo.getUserId()).getNickName());
//            // 修改日期格式，只保留到天
//            Instant instant = financeExpenditurePlanVo.getPaymentTime().toInstant(); // 将Date转换为Instant（时间戳）
//            LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault()); // 根据系统时区转换为LocalDateTime
//            LocalDateTime startOfDay = dateTime.toLocalDate().atStartOfDay(); // 获取当天的开始时间（00:00:00）
//            financeExpenditurePlanVo.setPaymentTime(Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant())); // 转换回Instant并生成新的Date对象

            return financeExpenditurePlanVo;
        }).collect(Collectors.toList());

    }


//    /**
//     * 总体流程监听(例如: 草稿，撤销，退回，作废，终止，已完成等)
//     * 正常使用只需#processEvent.flowCode=='notice'
//     * @param processEvent 参数
//     */
//    @EventListener(condition = "#processEvent.flowCode.equals('expenditurePlan')")
//    public void processHandler(ProcessEvent processEvent) {
//        log.info("当前任务执行了{}", processEvent.toString());
//        FinanceExpenditurePlan financeExpenditurePlan = baseMapper.selectById(Convert.toLong(processEvent.getBusinessId()));
//        financeExpenditurePlan.setFlowStatus(processEvent.getStatus());
//        // 用于例如审批附件 审批意见等 存储到业务表内 自行根据业务实现存储流程
//        Map<String, Object> params = processEvent.getParams();
//        if (MapUtil.isNotEmpty(params)) {
//            // 历史任务扩展(通常为附件)
//            String hisTaskExt = Convert.toStr(params.get("hisTaskExt"));
//            // 办理人
//            String handler = Convert.toStr(params.get("handler"));
//            // 办理意见
//            String message = Convert.toStr(params.get("message"));
//        }
//        if (processEvent.getSubmit()) {
//            financeExpenditurePlan.setFlowStatus(BusinessStatusEnum.WAITING.getStatus());
//        }
//        baseMapper.updateById(financeExpenditurePlan);
//    }
//
//    /**
//     * 执行任务创建监听
//     * 示例：也可通过  @EventListener(condition = "#processTaskEvent.flowCode=='leave1'")进行判断
//     * 在方法中判断流程节点key
//     * if ("xxx".equals(processTaskEvent.getNodeCode())) {
//     * //执行业务逻辑
//     * }
//     *
//     * @param processTaskEvent 参数
//     */
//    @EventListener(condition = "#ProcessTaskEvent.flowCode.equals('expenditurePlan')")
//    public void processTaskHandler(ProcessTaskEvent processTaskEvent) {
//        log.info("当前任务执行了{}", processTaskEvent.toString());
//
//        FinanceExpenditurePlan financeExpenditurePlan = baseMapper.selectById(Convert.toLong(processTaskEvent.getBusinessId()));
//        financeExpenditurePlan.setFlowStatus(BusinessStatusEnum.WAITING.getStatus());
//        baseMapper.updateById(financeExpenditurePlan);
//    }
//
//    /**
//     * 监听删除流程事件
//     * 正常使用只需#processDeleteEvent.flowCode=='leave1'
//     * 示例为了方便则使用startsWith匹配了全部示例key
//     *
//     * @param processDeleteEvent 参数
//     */
//    @EventListener(condition = "#processDeleteEvent.flowCode.equals('expenditurePlan')")
//    public void processDeleteHandler(ProcessDeleteEvent processDeleteEvent) {
//        log.info("监听删除流程事件，当前任务执行了{}", processDeleteEvent.toString());
//        FinanceExpenditurePlan financeExpenditurePlan = baseMapper.selectById(Convert.toLong(processDeleteEvent.getBusinessId()));
//        if (ObjectUtil.isNull(financeExpenditurePlan)) {
//            return;
//        }
//        baseMapper.deleteById(financeExpenditurePlan.getId());
//    }
}
