package org.dromara.system.service;

import org.dromara.system.domain.vo.SysStandardVo;
import org.dromara.system.domain.bo.SysStandardBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 标准Service接口
 *
 * <AUTHOR> Li
 * @date 2025-06-30
 */
public interface ISysStandardService {

    /**
     * 查询标准
     *
     * @param standardId 主键
     * @return 标准
     */
    SysStandardVo queryById(Long standardId);

    /**
     * 分页查询标准列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 标准分页列表
     */
    TableDataInfo<SysStandardVo> queryPageList(SysStandardBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的标准列表
     *
     * @param bo 查询条件
     * @return 标准列表
     */
    List<SysStandardVo> queryList(SysStandardBo bo);

    /**
     * 新增标准
     *
     * @param bo 标准
     * @return 是否新增成功
     */
    SysStandardVo insertByBo(SysStandardBo bo);

    /**
     * 修改标准
     *
     * @param bo 标准
     * @return 是否修改成功
     */
    SysStandardVo updateByBo(SysStandardBo bo);

    /**
     * 校验并批量删除标准信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
