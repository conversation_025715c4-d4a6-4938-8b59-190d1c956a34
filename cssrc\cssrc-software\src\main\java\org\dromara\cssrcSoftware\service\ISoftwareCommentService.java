package org.dromara.cssrcSoftware.service;

import org.dromara.cssrcSoftware.domain.vo.SoftwareCommentVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareCommentBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 软件评论Service接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface ISoftwareCommentService {

    /**
     * 查询软件评论
     *
     * @param commentId 主键
     * @return 软件评论
     */
    SoftwareCommentVo queryById(Long commentId);

    /**
     * 分页查询软件评论列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 软件评论分页列表
     */
    TableDataInfo<SoftwareCommentVo> queryPageList(SoftwareCommentBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的软件评论列表
     *
     * @param bo 查询条件
     * @return 软件评论列表
     */
    List<SoftwareCommentVo> queryList(SoftwareCommentBo bo);

    /**
     * 新增软件评论
     *
     * @param bo 软件评论
     * @return 是否新增成功
     */
    Boolean insertByBo(SoftwareCommentBo bo);

    /**
     * 修改软件评论
     *
     * @param bo 软件评论
     * @return 是否修改成功
     */
    Boolean updateByBo(SoftwareCommentBo bo);

    /**
     * 校验并批量删除软件评论信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
