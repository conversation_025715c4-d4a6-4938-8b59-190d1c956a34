package org.dromara.cssrcSoftware.service;

import org.dromara.cssrcSoftware.domain.vo.SoftwareWhiteuserVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareWhiteuserBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 软件权限白名单Service接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface ISoftwareWhiteuserService {

    /**
     * 查询软件权限白名单
     *
     * @param id 主键
     * @return 软件权限白名单
     */
    SoftwareWhiteuserVo queryById(Long id);

    /**
     * 分页查询软件权限白名单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 软件权限白名单分页列表
     */
    TableDataInfo<SoftwareWhiteuserVo> queryPageList(SoftwareWhiteuserBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的软件权限白名单列表
     *
     * @param bo 查询条件
     * @return 软件权限白名单列表
     */
    List<SoftwareWhiteuserVo> queryList(SoftwareWhiteuserBo bo);

    /**
     * 新增软件权限白名单
     *
     * @param bo 软件权限白名单
     * @return 是否新增成功
     */
    Boolean insertByBo(SoftwareWhiteuserBo bo);

    /**
     * 修改软件权限白名单
     *
     * @param bo 软件权限白名单
     * @return 是否修改成功
     */
    Boolean updateByBo(SoftwareWhiteuserBo bo);

    /**
     * 校验并批量删除软件权限白名单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
