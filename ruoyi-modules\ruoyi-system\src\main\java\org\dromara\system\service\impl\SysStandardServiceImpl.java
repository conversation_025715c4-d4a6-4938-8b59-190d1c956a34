package org.dromara.system.service.impl;

import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.ObjectUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.ISysOssService;
import org.springframework.stereotype.Service;
import org.dromara.system.domain.bo.SysStandardBo;
import org.dromara.system.domain.vo.SysStandardVo;
import org.dromara.system.domain.SysStandard;
import org.dromara.system.mapper.SysStandardMapper;
import org.dromara.system.service.ISysStandardService;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 标准Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-06-30
 */
@RequiredArgsConstructor
@Service
public class SysStandardServiceImpl implements ISysStandardService {

    private final SysStandardMapper baseMapper;
    private final ISysOssService ossService;

    /**
     * 查询标准
     *
     * @param standardId 主键
     * @return 标准
     */
    @Override
    public SysStandardVo queryById(Long standardId){
        return baseMapper.selectVoById(standardId);
    }

    /**
     * 分页查询标准列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 标准分页列表
     */
    @Override
    public TableDataInfo<SysStandardVo> queryPageList(SysStandardBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysStandard> lqw = buildQueryWrapper(bo);
        Page<SysStandardVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 在Java层面进行密级过滤
        LoginUser loginUser = LoginHelper.getLoginUser();
        List<SysStandardVo> filteredRecords;

        if (loginUser != null && StringUtils.isNotBlank(loginUser.getSecret())) {
            try {
                int userSecretLevel = Integer.parseInt(loginUser.getSecret());

                filteredRecords = result.getRecords().stream()
                    .filter(vo -> hasPermissionToView(vo, userSecretLevel))
                    .collect(Collectors.toList());

            } catch (NumberFormatException e) {
                throw new ServiceException("用户密级格式错误");
            }
        } else {
            // 未登录用户的处理
            filteredRecords = result.getRecords().stream()
                .filter(vo -> vo.getStandardFile() == null)
                .collect(Collectors.toList());
        }

        // 填充文件信息
        for (SysStandardVo vo : filteredRecords) {
            fillingDataAfterSave(vo);
        }

        // 创建新的分页结果，修正总数
        Page<SysStandardVo> filteredResult = new Page<>(result.getCurrent(), result.getSize());
        filteredResult.setRecords(filteredRecords);
        filteredResult.setTotal(filteredRecords.size()); // 关键：设置正确的总数

        return TableDataInfo.build(filteredResult);
    }


    /**
     * 查询符合条件的标准列表
     *
     * @param bo 查询条件
     * @return 标准列表
     */
    @Override
    public List<SysStandardVo> queryList(SysStandardBo bo) {
        LambdaQueryWrapper<SysStandard> lqw = buildQueryWrapper(bo);
        List<SysStandardVo> list = baseMapper.selectVoList(lqw);

        // 在Java层面进行密级过滤
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser != null && StringUtils.isNotBlank(loginUser.getSecret())) {
            try {
                int userSecretLevel = Integer.parseInt(loginUser.getSecret());

                return list.stream()
                    .filter(vo -> hasPermissionToView(vo, userSecretLevel))
                    .collect(Collectors.toList());

            } catch (NumberFormatException e) {
                throw new ServiceException("用户密级格式错误");
            }
        } else {
            return list.stream()
                .filter(vo -> vo.getStandardFile() == null)
                .collect(Collectors.toList());
        }
    }

    private LambdaQueryWrapper<SysStandard> buildQueryWrapper(SysStandardBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysStandard> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SysStandard::getStandardId);
        lqw.like(StringUtils.isNotBlank(bo.getStandardCode()), SysStandard::getStandardCode, bo.getStandardCode());
        lqw.like(StringUtils.isNotBlank(bo.getStandardName()), SysStandard::getStandardName, bo.getStandardName());
        lqw.eq(StringUtils.isNotBlank(bo.getStandardStatus()), SysStandard::getStandardStatus, bo.getStandardStatus());
        lqw.eq(bo.getStandardFile() != null, SysStandard::getStandardFile, bo.getStandardFile());

        // 由于 file_secret 字段是加密的，无法在 SQL 中直接比较
        // 只在这里做基本的查询条件，密级过滤在 Java 层面处理
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            String tenantId = loginUser.getTenantId();
            lqw.eq(SysStandard::getTenantId, tenantId);
        }

        return lqw;
    }


    /**
     * 检查用户是否有权限查看该标准记录
     */
    private boolean hasPermissionToView(SysStandardVo vo, int userSecretLevel) {
        // 如果标准密级直接高于用户密级则不予显示
        if (vo.getSecret() == null || Integer.parseInt(vo.getSecret() ) > userSecretLevel) {
            return false;
        }

        // 如果标准密级低于用户密级需要检查文件密级（虽然可能不太需要了，但是保留代码之后可以抄
        if (vo.getStandardFile() == null) {
            return true; // 没有关联文件的记录可以查看
        }

        SysOssVo oss = ossService.getById(vo.getStandardFile());
        if (oss == null || StringUtils.isBlank(oss.getFileSecret())) {
            return true; // 文件不存在或没有密级设置
        }

        try {
            // 这里 oss.getFileSecret() 会自动解密，返回原始值
            int fileSecretLevel = Integer.parseInt(oss.getFileSecret());
            return fileSecretLevel <= userSecretLevel;
        } catch (NumberFormatException e) {
            return false; // 文件密级格式错误，不显示
        }
    }



    /**
     * 新增标准
     *
     * @param bo 标准
     * @return 是否新增成功
     */
    @Override
    public SysStandardVo insertByBo(SysStandardBo bo) {
        SysStandard add = MapstructUtils.convert(bo, SysStandard.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
//        if (flag) {
//            bo.setStandardId(add.getStandardId());
//        }
        SysStandardVo vo = MapstructUtils.convert(add, SysStandardVo.class);
        return fillingDataAfterSave(vo);
    }

    /**
     * 修改标准
     *
     * @param bo 标准
     * @return 是否修改成功
     */
    @Override
    public SysStandardVo updateByBo(SysStandardBo bo) {
        SysStandard update = MapstructUtils.convert(bo, SysStandard.class);
        validEntityBeforeSave(update);
        int flag = baseMapper.updateById(update);
        if (flag < 1) {
            throw new ServiceException("修改标准" + update.getStandardId() + "失败");
        }

        SysStandardVo vo = MapstructUtils.convert(update, SysStandardVo.class);
        return fillingDataAfterSave(vo);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysStandard entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 返回 Vo 给前端前填充必要信息，例如根据存储的oss_id填充文件信息
     * 因为仅存储 oss_id 不够前端使用
     * @param vo
     * @return
     */
    private SysStandardVo fillingDataAfterSave(SysStandardVo vo) {
        if (vo.getStandardFile() != null) {
            SysOssVo oss = ossService.getById(vo.getStandardFile());
            if (oss != null) {
//                // 二次验证权限
//                LoginUser user = LoginHelper.getLoginUser();
//                if (user != null && StringUtils.isNotBlank(user.getSecret())) {
//                    try {
//                        int userLevel = Integer.parseInt(user.getSecret());
//                        int fileLevel = Integer.parseInt(oss.getFileSecret());
//                        if (fileLevel > userLevel) {
//                            // 无权限时返回空文件信息
//                            vo.setStandardFile(null);
//                            vo.setStandardFileName("无权限查看");
//                            return vo;
//                        }
//                    } catch (NumberFormatException ignored) {}
//                }
                // 填充文件数据
                vo.setStandardFileName(oss.getOriginalName());
                vo.setStandardFileSecret(oss.getFileSecret());
                vo.setStandardFileUrl(oss.getUrl());
            }
        }
        return vo;
    }


    /**
     * 校验并批量删除标准信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
