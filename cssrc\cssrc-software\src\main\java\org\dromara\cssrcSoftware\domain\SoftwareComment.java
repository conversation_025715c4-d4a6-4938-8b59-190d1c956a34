package org.dromara.cssrcSoftware.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 软件评论对象 software_comment
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("software_comment")
@Schema(description = "软件评论")
public class SoftwareComment extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 评论ID
     */
    @TableId(value = "comment_id")
    private Long commentId;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 回复哪条评论
     */
    private Long replyTo;

    /**
     * 状态（0正常 1屏蔽）
     */
    private String status;


}
