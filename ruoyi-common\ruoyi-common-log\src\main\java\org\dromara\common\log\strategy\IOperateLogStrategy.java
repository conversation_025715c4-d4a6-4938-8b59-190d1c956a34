package org.dromara.common.log.strategy;

import java.lang.reflect.InvocationTargetException;

/**
 * 操作日志内容信息生成接口
 * <AUTHOR>
 */
public interface IOperateLogStrategy {

    /**
     * 生成操作日志内容
     * @param tableEntity 表实体类数组
     * @param operatorObjId  操作对象ID
     * @return 操作日志内容
     */
    String getOperatorContent(Object requestObj, Class<?>[] tableEntity, String operatorObjId) throws IllegalAccessException, InvocationTargetException, NoSuchMethodException;

}
