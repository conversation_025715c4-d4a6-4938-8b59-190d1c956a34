package org.dromara.cssrcSoftware.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.ObjectUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.cssrcSoftware.domain.SoftwareInfo;
import org.dromara.cssrcSoftware.domain.SoftwareVersion;
import org.dromara.cssrcSoftware.domain.vo.SoftwareInfoVo;
import org.dromara.cssrcSoftware.domain.vo.SoftwareVersionVo;
import org.dromara.cssrcSoftware.mapper.SoftwareInfoMapper;
import org.dromara.cssrcSoftware.mapper.SoftwareVersionMapper;
import org.springframework.stereotype.Service;
import org.dromara.cssrcSoftware.domain.bo.SoftwareDownloadLogBo;
import org.dromara.cssrcSoftware.domain.vo.SoftwareDownloadLogVo;
import org.dromara.cssrcSoftware.domain.SoftwareDownloadLog;
import org.dromara.cssrcSoftware.mapper.SoftwareDownloadLogMapper;
import org.dromara.cssrcSoftware.service.ISoftwareDownloadLogService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 软件下载日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RequiredArgsConstructor
@Service
public class SoftwareDownloadLogServiceImpl implements ISoftwareDownloadLogService {

    private final SoftwareDownloadLogMapper baseMapper;
    private final SoftwareInfoMapper softwareInfoMapper;
    private final SoftwareVersionMapper softwareVersionMapper;

    /**
     * 查询软件下载日志
     *
     * @param logId 主键
     * @return 软件下载日志
     */
    @Override
    public SoftwareDownloadLogVo queryById(Long logId){
        return baseMapper.selectVoById(logId);
    }

    /**
     * 分页查询软件下载日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 软件下载日志分页列表
     */
    @Override
    public TableDataInfo<SoftwareDownloadLogVo> queryPageList(SoftwareDownloadLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SoftwareDownloadLog> lqw = buildQueryWrapper(bo);
        Page<SoftwareDownloadLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        List<SoftwareDownloadLogVo> records = result.getRecords();
        fillingDataAfterSave(records);
        result.setRecords(records);

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的软件下载日志列表
     *
     * @param bo 查询条件
     * @return 软件下载日志列表
     */
    @Override
    public List<SoftwareDownloadLogVo> queryList(SoftwareDownloadLogBo bo) {
        LambdaQueryWrapper<SoftwareDownloadLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SoftwareDownloadLog> buildQueryWrapper(SoftwareDownloadLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SoftwareDownloadLog> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SoftwareDownloadLog::getLogId);
        lqw.eq(bo.getSoftwareId() != null, SoftwareDownloadLog::getSoftwareId, bo.getSoftwareId());
        lqw.eq(bo.getVersionId() != null, SoftwareDownloadLog::getVersionId, bo.getVersionId());
        lqw.eq(bo.getUserId() != null, SoftwareDownloadLog::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getIpAddress()), SoftwareDownloadLog::getIpAddress, bo.getIpAddress());
        lqw.eq(bo.getDownloadTime() != null, SoftwareDownloadLog::getDownloadTime, bo.getDownloadTime());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SoftwareDownloadLog::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增软件下载日志
     *
     * @param bo 软件下载日志
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SoftwareDownloadLogBo bo) {
        SoftwareDownloadLog add = MapstructUtils.convert(bo, SoftwareDownloadLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setLogId(add.getLogId());
        }
        return flag;
    }

    /**
     * 修改软件下载日志
     *
     * @param bo 软件下载日志
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SoftwareDownloadLogBo bo) {
        SoftwareDownloadLog update = MapstructUtils.convert(bo, SoftwareDownloadLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SoftwareDownloadLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除软件下载日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


    /**
     * 返回 List Vo 给前端前填充必要信息
     * @param vos
     * @return
     */
    private List<SoftwareDownloadLogVo> fillingDataAfterSave(List<SoftwareDownloadLogVo> vos) {
        if (vos == null || vos.isEmpty()) {
            return vos;
        }

        Set<Long> allSoftwareIds = new HashSet<>();
        Set<Long> allVersionIds = new HashSet<>();

        for (SoftwareDownloadLogVo vo : vos) {
            addValidIds(vo.getSoftwareId(), allSoftwareIds);
            addValidIds(vo.getVersionId(), allVersionIds);
        }

        // 批量查询软件和版本信息
        Map<Long, SoftwareInfoVo> softwareInfoMap = getSoftwareMap(allSoftwareIds);
        Map<Long, SoftwareVersionVo> softwareVersionMap = getVersionMap(allVersionIds);

        // 回填
        for (SoftwareDownloadLogVo vo : vos) {
            vo.setSoftwareName(softwareInfoMap.get(vo.getSoftwareId()).getSoftwareName());
            vo.setVersionName(softwareVersionMap.get(vo.getVersionId()).getVersionName());
        }

        return vos;
    }

    // 解析并添加合法 ID
    private void addValidIds(Long id, Set<Long> result) {
        if (id != null && id > 0) {
            result.add(id);
        }
    }

    // 获取软件信息 Map（支持批量查询）
    private Map<Long, SoftwareInfoVo> getSoftwareMap(Set<Long> ids) {
        if (ids.isEmpty()) return Collections.emptyMap();
        List<SoftwareInfoVo> procedureVos = softwareInfoMapper.selectVoByIds(ids);
        return procedureVos.stream()
            .collect(Collectors.toMap(SoftwareInfoVo::getSoftwareId, v -> v));
    }

    // 获取试件 Map（支持批量查询）
    private Map<Long, SoftwareVersionVo> getVersionMap(Set<Long> ids) {
        if (ids.isEmpty()) return Collections.emptyMap();
        List<SoftwareVersionVo> specimenVos = softwareVersionMapper.selectVoByIds(ids);
        return specimenVos.stream()
            .collect(Collectors.toMap(SoftwareVersionVo::getVersionId, v -> v));
    }
}
