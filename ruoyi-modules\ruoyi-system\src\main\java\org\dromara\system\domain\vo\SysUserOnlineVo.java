package org.dromara.system.domain.vo;

import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.system.domain.SysUserOnline;

import java.io.Serial;
import java.io.Serializable;

/**
 * 在线用户视图对象 sys_user_online
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysUserOnline.class)
public class SysUserOnlineVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会话编号
     */
    @ExcelProperty(value = "会话编号")
    private String tokenId;

    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    private String deptName;

    /**
     * 用户名称
     */
    @ExcelProperty(value = "用户名称")
    private String userName;

    /**
     * 客户端
     */
    @ExcelProperty(value = "客户端")
    private String clientKey;

    /**
     * 设备类型
     */
    @ExcelProperty(value = "设备类型")
    private String deviceType;

    /**
     * 登录IP地址
     */
    @ExcelProperty(value = "登录IP地址")
    private String ipaddr;

    /**
     * 登录地址
     */
    @ExcelProperty(value = "登录地址")
    private String loginLocation;

    /**
     * 浏览器类型
     */
    @ExcelProperty(value = "浏览器类型")
    private String browser;

    /**
     * 操作系统
     */
    @ExcelProperty(value = "操作系统")
    private String os;

    /**
     * 登录时间
     */
    @ExcelProperty(value = "登录时间")
    private Long loginTime;

}
