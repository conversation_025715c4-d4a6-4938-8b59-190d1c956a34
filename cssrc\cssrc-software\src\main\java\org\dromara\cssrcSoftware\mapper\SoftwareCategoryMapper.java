package org.dromara.cssrcSoftware.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.dromara.common.mybatis.helper.DataBaseHelper;
import org.dromara.cssrcSoftware.domain.SoftwareCategory;
import org.dromara.cssrcSoftware.domain.vo.SoftwareCategoryVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 软件分类Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@DS("software")
public interface SoftwareCategoryMapper extends BaseMapperPlus<SoftwareCategory, SoftwareCategoryVo> {
    /**
     * 根据父软件分类ID查询其所有子软件分类的列表
     *
     * @param parentId 父软件分类ID
     * @return 包含子软件分类的列表
     */
    default List<SoftwareCategory> selectListByParentId(Long parentId) {
        return this.selectList(new LambdaQueryWrapper<SoftwareCategory>()
            .select(SoftwareCategory::getCategoryId)
            .eq(SoftwareCategory::getCategoryId, parentId)
            .or()
            .apply(DataBaseHelper.findInSet(parentId, "ancestors")));
    }
}
