package org.dromara.common.log.utils;

import org.dromara.common.log.enums.BusinessType;

public class OperatorLogContext {
    private static final ThreadLocal<Object> OLD_DATA = new ThreadLocal<>();
    private static final ThreadLocal<Object> NEW_DATA = new ThreadLocal<>();
    private static final ThreadLocal<BusinessType> BUSINESS_TYPE = new ThreadLocal<>();

    public static void setOldData(Object data) {
        OLD_DATA.set(data);
    }

    public static Object getOldData() {
        return OLD_DATA.get();
    }

    public static void clear() {
        OLD_DATA.remove();
        NEW_DATA.remove();
    }

    public static void setNewData(Object data) {
        NEW_DATA.set(data);
    }

    public static Object getNewData() {
        return NEW_DATA.get();
    }

    public static void setBusinessType(BusinessType businessType) {
        BUSINESS_TYPE.set(businessType);
    }

    public static BusinessType getBusinessType() {
        return BUSINESS_TYPE.get();
}

}
