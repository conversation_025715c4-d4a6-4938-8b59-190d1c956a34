package org.dromara.common.redis.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.dromara.common.core.utils.SpringUtils;
import org.redisson.api.RBloomFilter;
import org.redisson.api.RedissonClient;

import java.time.Duration;

/**
 * 布隆过滤器工具类
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class BloomFilterUtils {

    private static final RedissonClient CLIENT = SpringUtils.getBean(RedissonClient.class);

    /**
     * 创建布隆过滤器
     *
     * @param filterName 过滤器名称
     * @param expectedInsertions 预测插入数量
     * @param falseProbability 误判率
     */
    public static <T> RBloomFilter<T> create(String filterName, long expectedInsertions, double falseProbability) {
        RBloomFilter<T> bloomFilter = CLIENT.getBloomFilter(filterName);
        bloomFilter.tryInit(expectedInsertions, falseProbability);
        return bloomFilter;
    }

    /**
     * 创建布隆过滤器(带过期时间：单位是天)
     *
     * @param filterName 过滤器名称
     * @param expectedInsertions 预测插入数量
     * @param falseProbability 误判率
     * @param expireTime 过期时间（天）
     */
    public static <T> RBloomFilter<T> create(String filterName, long expectedInsertions, double falseProbability, int expireTime) {
        RBloomFilter<T> bloomFilter = CLIENT.getBloomFilter(filterName);
        bloomFilter.tryInit(expectedInsertions, falseProbability);
        // 设置过期时间为 expireTime 天
        RedisUtils.expire(filterName, Duration.ofDays(expireTime));
        return bloomFilter;
    }

    /**
     * 获取布隆过滤器
     */
    public static <T> RBloomFilter<T> getFilter(String filterName) {
        return CLIENT.getBloomFilter(filterName);
    }

    /**
     * 添加元素
     *
     * @param filterName 过滤器名称
     * @param value 值
     */
    public static <T> boolean add(String filterName, T value) {
        RBloomFilter<T> bloomFilter = CLIENT.getBloomFilter(filterName);
        return bloomFilter.add(value);
    }

    /**
     * 包含元素
     *
     * @param filterName 过滤器名称
     * @param value 值
     */
    public static <T> boolean contains(String filterName, T value) {
        RBloomFilter<T> bloomFilter = CLIENT.getBloomFilter(filterName);
        return bloomFilter.contains(value);
    }

    /**
     * 获取布隆过滤器包含元素数量
     */
    public static long getCount(String filterName) {
        RBloomFilter<String> bloomFilter = CLIENT.getBloomFilter(filterName);
        return bloomFilter.count();
    }

    /**
     * 删除布隆过滤器
     */
    public static boolean delete(String filterName) {
        RBloomFilter<String> bloomFilter = CLIENT.getBloomFilter(filterName);
        return bloomFilter.delete();
    }

    /**
     * 清空布隆过滤器
     */
    public static <T> void clear(String filterName) {
        RBloomFilter<T> bloomFilter = CLIENT.getBloomFilter(filterName);
        bloomFilter.delete();  // Redisson的布隆过滤器使用delete替代clear
    }

}
