package org.dromara.cssrcSoftware.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.dromara.cssrcSoftware.domain.bo.SoftwareAccessBo;
import org.dromara.cssrcSoftware.domain.vo.SoftwareAccessVo;
import org.dromara.cssrcSoftware.domain.SoftwareAccess;
import org.dromara.cssrcSoftware.mapper.SoftwareAccessMapper;
import org.dromara.cssrcSoftware.service.ISoftwareAccessService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 软件入网申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SoftwareAccessServiceImpl implements ISoftwareAccessService {

    private final SoftwareAccessMapper baseMapper;

    /**
     * 查询软件入网申请
     *
     * @param accessId 主键
     * @return 软件入网申请
     */
    @Override
    public SoftwareAccessVo queryById(Long accessId){
        return baseMapper.selectVoById(accessId);
    }

    /**
     * 分页查询软件入网申请列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 软件入网申请分页列表
     */
    @Override
    public TableDataInfo<SoftwareAccessVo> queryPageList(SoftwareAccessBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SoftwareAccess> lqw = buildQueryWrapper(bo);
        Page<SoftwareAccessVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的软件入网申请列表
     *
     * @param bo 查询条件
     * @return 软件入网申请列表
     */
    @Override
    public List<SoftwareAccessVo> queryList(SoftwareAccessBo bo) {
        LambdaQueryWrapper<SoftwareAccess> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SoftwareAccess> buildQueryWrapper(SoftwareAccessBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SoftwareAccess> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SoftwareAccess::getAccessId);
        lqw.eq(StringUtils.isNotBlank(bo.getAccessType()), SoftwareAccess::getAccessType, bo.getAccessType());
        lqw.eq(bo.getAccessResponsible() != null, SoftwareAccess::getAccessResponsible, bo.getAccessResponsible());
        lqw.eq(StringUtils.isNotBlank(bo.getNetworkType()), SoftwareAccess::getNetworkType, bo.getNetworkType());
        lqw.eq(StringUtils.isNotBlank(bo.getComputerCode()), SoftwareAccess::getComputerCode, bo.getComputerCode());
        lqw.eq(StringUtils.isNotBlank(bo.getFlowStatus()), SoftwareAccess::getFlowStatus, bo.getFlowStatus());
        return lqw;
    }

    /**
     * 新增软件入网申请
     *
     * @param bo 软件入网申请
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SoftwareAccessBo bo) {
        SoftwareAccess add = MapstructUtils.convert(bo, SoftwareAccess.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAccessId(add.getAccessId());
        }
        return flag;
    }

    /**
     * 修改软件入网申请
     *
     * @param bo 软件入网申请
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SoftwareAccessBo bo) {
        SoftwareAccess update = MapstructUtils.convert(bo, SoftwareAccess.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SoftwareAccess entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除软件入网申请信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
