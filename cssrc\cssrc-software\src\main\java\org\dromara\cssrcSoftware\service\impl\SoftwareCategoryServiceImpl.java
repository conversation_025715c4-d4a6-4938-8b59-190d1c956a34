package org.dromara.cssrcSoftware.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.TreeBuildUtils;
import org.springframework.stereotype.Service;
import org.dromara.cssrcSoftware.domain.bo.SoftwareCategoryBo;
import org.dromara.cssrcSoftware.domain.vo.SoftwareCategoryVo;
import org.dromara.cssrcSoftware.domain.SoftwareCategory;
import org.dromara.cssrcSoftware.mapper.SoftwareCategoryMapper;
import org.dromara.cssrcSoftware.service.ISoftwareCategoryService;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 软件分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RequiredArgsConstructor
@Service
public class SoftwareCategoryServiceImpl implements ISoftwareCategoryService {

    private final SoftwareCategoryMapper baseMapper;

    /**
     * 查询软件分类
     *
     * @param categoryId 主键
     * @return 软件分类
     */
    @Override
    public SoftwareCategoryVo queryById(Long categoryId){
        return baseMapper.selectVoById(categoryId);
    }


    /**
     * 查询符合条件的软件分类列表
     *
     * @param bo 查询条件
     * @return 软件分类列表
     */
    @Override
    public List<SoftwareCategoryVo> queryList(SoftwareCategoryBo bo) {
        LambdaQueryWrapper<SoftwareCategory> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SoftwareCategory> buildQueryWrapper(SoftwareCategoryBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SoftwareCategory> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SoftwareCategory::getCategoryId);
        lqw.like(StringUtils.isNotBlank(bo.getCategoryName()), SoftwareCategory::getCategoryName, bo.getCategoryName());
        lqw.eq(bo.getParentId() != null, SoftwareCategory::getParentId, bo.getParentId());
        lqw.eq(StringUtils.isNotBlank(bo.getAncestors()), SoftwareCategory::getAncestors, bo.getAncestors());
        lqw.eq(StringUtils.isNotBlank(bo.getSoftwareRange()), SoftwareCategory::getSoftwareRange, bo.getSoftwareRange());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SoftwareCategory::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 查询软件分类树结构信息
     *
     * @param bo 软件分类信息
     * @return 软件分类信息集合
     */
    @Override
    public List<Tree<Long>> selectCategoryTreeList(SoftwareCategoryBo bo) {
        LambdaQueryWrapper<SoftwareCategory> lqw = buildQueryWrapper(bo);
        List<SoftwareCategoryVo> list = baseMapper.selectVoList(lqw);

        // 1. 统一根节点 parentId 为 0
        list.forEach(item -> {
            if (item.getParentId() == null) {
                item.setParentId(0L);
            }
        });

        // 2. 验证数据完整性
        Set<Long> allIds = list.stream()
            .map(SoftwareCategoryVo::getCategoryId)
            .collect(Collectors.toSet());

        for (SoftwareCategoryVo node : list) {
            if (node.getParentId() != 0L && !allIds.contains(node.getParentId())) {
                throw new IllegalArgumentException("节点 ID: " + node.getCategoryId() + " 的父节点不存在");
            }
        }

        // 3. 构建树结构
        return buildCategoryTreeSelect(list);
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param categorys 软件分类列表
     * @return 下拉树结构列表
     */
    @Override
    public List<Tree<Long>> buildCategoryTreeSelect(List<SoftwareCategoryVo> categorys) {
        if (CollUtil.isEmpty(categorys)) {
            return CollUtil.newArrayList();
        }
        return TreeBuildUtils.buildMultiRoot(
            categorys,
            SoftwareCategoryVo::getCategoryId,
            SoftwareCategoryVo::getParentId,
            (node, treeNode) -> treeNode
                .setId(node.getCategoryId())
                .setParentId(node.getParentId())
                .setName(node.getCategoryName())
                .setWeight(node.getOrderNum())
                .putExtra("disabled", SystemConstants.DISABLE.equals(node.getStatus()))
        );
    }

    /**
     * 新增软件分类
     *
     * @param bo 软件分类
     * @return 是否新增成功
     */
    @Override
    public SoftwareCategoryVo insertByBo(SoftwareCategoryBo bo) {
        SoftwareCategory add = MapstructUtils.convert(bo, SoftwareCategory.class);

        // 填充祖级列表
        SoftwareCategory parentFolder = baseMapper.selectById(bo.getParentId());
        add.setAncestors(parentFolder.getAncestors() + StringUtils.SEPARATOR + add.getParentId());

        validEntityBeforeSave(add);

        int flag = baseMapper.insert(add);
        if (flag < 1) {
            throw new ServiceException("新增软件分类" + add.getCategoryName() + "失败");
        }

        SoftwareCategoryVo vo = MapstructUtils.convert(add, SoftwareCategoryVo.class);
        return fillingDataAfterSave(vo);
    }

    /**
     * 修改软件分类
     *
     * @param bo 软件分类
     * @return 是否修改成功
     */
    @Override
    public SoftwareCategoryVo updateByBo(SoftwareCategoryBo bo) {
        SoftwareCategory update = MapstructUtils.convert(bo, SoftwareCategory.class);
        validEntityBeforeSave(update);
        int flag = baseMapper.updateById(update);
        if(flag < 1) {
            throw new ServiceException("修改软件分类" + update.getCategoryName() + "失败");
        }
        SoftwareCategoryVo vo = MapstructUtils.convert(update, SoftwareCategoryVo.class);
        return fillingDataAfterSave(vo);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SoftwareCategory entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 返回 Vo 给前端前填充必要信息
     * @param vo
     * @return
     */
    private SoftwareCategoryVo fillingDataAfterSave(SoftwareCategoryVo vo){
        return vo;
    }

    /**
     * 返回 List Vo 给前端前填充必要信息
     * @param vos
     * @return
     */
    private List<SoftwareCategoryVo> fillingDataAfterSave(List<SoftwareCategoryVo> vos) {
        if (vos == null || vos.isEmpty()) {
            return vos;
        }

        return vos;
    }

    /**
     * 校验并批量删除软件分类信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 校验软件分类名称是否唯一
     *
     * @param bo 软件分类信息
     * @return 结果
     */
    @Override
    public boolean checkSoftwareCategoryUnique(SoftwareCategoryBo bo) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SoftwareCategory>()
            .eq(SoftwareCategory::getCategoryName, bo.getCategoryName())
            .eq(SoftwareCategory::getCategoryId, bo.getCategoryId())
            .ne(ObjectUtil.isNotNull(bo.getCategoryId()), SoftwareCategory::getCategoryId, bo.getCategoryId()));
        return !exist;
    }

    /**
     * 是否存在子节点
     *
     * @param categoryId 软件分类ID
     * @return 结果
     */
    @Override
    public boolean hasChildByCategoryId(Long categoryId) {
        return baseMapper.exists(new LambdaQueryWrapper<SoftwareCategory>()
            .eq(SoftwareCategory::getParentId, categoryId));
    }
}
