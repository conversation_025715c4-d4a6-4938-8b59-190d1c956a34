<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SysNoticeMapper">

    <resultMap type="org.dromara.system.domain.vo.SysNoticeVo" id="SysNoticeResult">
        <id property="noticeId" column="notice_id"/>
        <result property="noticeTitle" column="notice_title"/>
        <result property="noticeType" column="notice_type"/>
        <result property="noticeSource" column="notice_source"/>
        <result property="noticeContent" column="notice_content"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isTop" column="is_top"/>
        <result property="topDay" column="top_day"/>
        <result property="viewCount" column="view_count"/>
        <result property="flowStatus" column="flow_status"/>
        <result property="secret" column="secret"/>
    </resultMap>

    <select id="selectVoPageWithPermission" resultMap="SysNoticeResult">
        SELECT
            n.notice_id,
            n.notice_title,
            n.notice_type,
            n.notice_source,
            n.notice_content,
            n.status,
            n.remark,
            n.create_by,
            n.create_time,
            n.update_time,
            n.is_top,
            n.top_day,
            n.view_count,
            n.flow_status,
            n.secret
        FROM sys_notice n
        ${ew.customSqlSegment}
    </select>


    <update id="increaseViewCount">
        update sys_notice set view_count = view_count + 1 where notice_id = #{noticeId}
    </update>




</mapper>
