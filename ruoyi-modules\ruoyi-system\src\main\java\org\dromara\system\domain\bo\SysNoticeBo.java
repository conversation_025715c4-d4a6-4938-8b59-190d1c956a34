package org.dromara.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.core.xss.Xss;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.system.domain.SysNotice;

/**
 * 通知公告业务对象 sys_notice
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysNotice.class, reverseConvertGenerate = false)
public class SysNoticeBo extends BaseEntity {

    /**
     * 公告ID
     */
    private Long noticeId;

    /**
     * 公告标题
     */
    @Xss(message = "公告标题不能包含脚本字符")
    @NotBlank(message = "公告标题不能为空")
    @Size(min = 0, max = 50, message = "公告标题不能超过{max}个字符")
    private String noticeTitle;

    /**
     * 公告类型（1通知 2公告）
     */
    private String noticeType;

    /**
     * 公告内容
     */
    private String noticeContent;

    /**
     * 公告状态（0正常 1关闭）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人工号
     */
    private String createByName;

    /**
     * 创建人姓名
     */
    private String createByNickName;

    /**
     * 通知公告文件附件ossIds
     * 关联附件
     */
    private Long[] ossIds;

    /**
     * 置顶标志（0正常 1置顶）
     */
    @NotBlank(message = "置顶标志（0正常 1置顶）不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(min = 0, max = 1, message = "置顶标志不能超过{max}个字符")
    private String isTop;

    /**
     * 置顶天数
     */
    private int topDay;

    /**
     * 状态
     */
    private String flowStatus;

    /**
     * 公告密级
     */
    @NotNull(message = "公告密级不能为空", groups = { AddGroup.class, EditGroup.class })
    private String secret;

    /**
     * 公告图片
     */
    private String imgOssIds;
}
