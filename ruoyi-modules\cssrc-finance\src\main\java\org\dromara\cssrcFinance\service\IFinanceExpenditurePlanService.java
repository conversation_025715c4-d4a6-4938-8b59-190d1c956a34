package org.dromara.cssrcFinance.service;

import org.dromara.cssrcFinance.domain.vo.FinanceExpenditurePlanVo;
import org.dromara.cssrcFinance.domain.bo.FinanceExpenditurePlanBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 财务-预算-支出计划Service接口
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
public interface IFinanceExpenditurePlanService {

    /**
     * 查询财务-预算-支出计划
     *
     * @param id 主键
     * @return 财务-预算-支出计划
     */
    FinanceExpenditurePlanVo queryById(Long id);

    /**
     * 分页查询财务-预算-支出计划列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 财务-预算-支出计划分页列表
     */
    TableDataInfo<FinanceExpenditurePlanVo> queryPageList(FinanceExpenditurePlanBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的财务-预算-支出计划列表
     *
     * @param bo 查询条件
     * @return 财务-预算-支出计划列表
     */
    List<FinanceExpenditurePlanVo> queryList(FinanceExpenditurePlanBo bo);

    /**
     * 新增财务-预算-支出计划
     *
     * @param bo 财务-预算-支出计划
     * @return 是否新增成功
     */
    FinanceExpenditurePlanVo insertByBo(FinanceExpenditurePlanBo bo);

    /**
     * 修改财务-预算-支出计划
     *
     * @param bo 财务-预算-支出计划
     * @return 是否修改成功
     */
    FinanceExpenditurePlanVo updateByBo(FinanceExpenditurePlanBo bo);

    /**
     * 校验并批量删除财务-预算-支出计划信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 处理导出到excel的数据
     *
     * @param list
     */
    void handleExportExcelData(List<FinanceExpenditurePlanVo> list);
}
