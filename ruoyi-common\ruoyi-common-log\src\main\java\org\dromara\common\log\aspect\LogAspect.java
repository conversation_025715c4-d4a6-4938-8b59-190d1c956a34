package org.dromara.common.log.aspect;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessStatus;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.log.event.OperLogEvent;
import org.dromara.common.log.strategy.*;
import org.dromara.common.log.utils.OperatorLogContext;
import org.dromara.common.log.utils.OperatorLogQueryUtil;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.http.HttpMethod;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 操作日志记录处理
 *
 * <AUTHOR> Li
 */
@Slf4j
@Aspect
@AutoConfiguration
public class LogAspect {

    /**
     * 排除敏感属性字段
     */
    public static final String[] EXCLUDE_PROPERTIES = { "password", "oldPassword", "newPassword", "confirmPassword" };


    /**
     * 计时 key
     */
    private static final ThreadLocal<StopWatch> KEY_CACHE = new ThreadLocal<>();


    /**
     * 处理请求前执行
     */
    @Before(value = "@annotation(controllerLog)")
    public void doBefore(JoinPoint joinPoint, Log controllerLog) {
        // 计时
        StopWatch stopWatch = new StopWatch();
        KEY_CACHE.set(stopWatch);
        stopWatch.start();

        // 只有非新增和非查询操作才需要获取原数据
        if (controllerLog.businessType() != BusinessType.INSERT && controllerLog.businessType() != BusinessType.SEARCH) {
            String operatorObjId = getOperatorObjId(joinPoint, controllerLog.operatorObjIdSpel(), null);
            if (StringUtils.isNotEmpty(operatorObjId)) {
                // 处理批量删除的情况
                if (operatorObjId.startsWith("[") && operatorObjId.endsWith("]")) {
                    String[] ids = operatorObjId.substring(1, operatorObjId.length() - 1).split(", ");
                    List<Object> oldDataList = new ArrayList<>();
                    for (String id : ids) {
                        Object oldData = OperatorLogQueryUtil.getObj(id, controllerLog.tableEntity());
                        if (oldData != null) {
                            oldDataList.add(oldData);
                        }
                    }
                    OperatorLogContext.setOldData(oldDataList);
                } else {
                    // 单个删除的情况
                    Object oldData = OperatorLogQueryUtil.getObj(operatorObjId, controllerLog.tableEntity());
                    OperatorLogContext.setOldData(oldData);
                }
            }
        }
    }



    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "@annotation(controllerLog)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, Log controllerLog, Object jsonResult) {
        handleLog(joinPoint, controllerLog, null, jsonResult);
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(value = "@annotation(controllerLog)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Log controllerLog, Exception e) {
        handleLog(joinPoint, controllerLog, e, null);
    }

    protected void handleLog(final JoinPoint joinPoint, Log controllerLog, final Exception e, Object jsonResult) {
        try {

            // *========数据库日志=========*//
            OperLogEvent operLog = new OperLogEvent();
            operLog.setTenantId(LoginHelper.getTenantId());
            operLog.setStatus(BusinessStatus.SUCCESS.ordinal());
            // 请求的地址
            String ip = ServletUtils.getClientIP();
            operLog.setOperIp(ip);
            operLog.setOperUrl(StringUtils.substring(ServletUtils.getRequest().getRequestURI(), 0, 255));
            LoginUser loginUser = LoginHelper.getLoginUser();
            operLog.setOperName(loginUser.getUsername());
            operLog.setOperNickName(loginUser.getNickname());
            operLog.setDeptName(loginUser.getDeptName());
            operLog.setSecret(loginUser.getSecret());

            if (e != null) {
                operLog.setStatus(BusinessStatus.FAIL.ordinal());
                operLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 3800));
            }
            // 设置方法名称
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = joinPoint.getSignature().getName();
            operLog.setMethod(className + "." + methodName + "()");
            // 设置请求方式
            operLog.setRequestMethod(ServletUtils.getRequest().getMethod());
            // 处理设置注解上的参数
            getControllerMethodDescription(joinPoint, controllerLog, operLog, jsonResult);
            // 设置消耗时间
            StopWatch stopWatch = KEY_CACHE.get();
            stopWatch.stop();
            operLog.setCostTime(stopWatch.getDuration().toMillis());
            // 发布事件保存数据库
            SpringUtils.context().publishEvent(operLog);
        } catch (Exception exp) {
            // 记录本地异常日志
            log.error("异常信息:{}", exp.getMessage());
        } finally {
            KEY_CACHE.remove();
        }
    }
    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param joinPoint 切点对象，用于获取方法签名等信息
     * @param log 日志配置对象，包含业务类型、标题、请求对象表达式、操作对象ID表达式、表实体类、操作类型、是否需要保存请求数据等信息
     * @param operLog 操作日志对象，用于记录操作日志信息
     * @param jsonResult 控制器方法的返回结果，通常是一个JSON对象
     * @throws Exception 如果在执行过程中出现异常，则抛出该异常
     */
    public void getControllerMethodDescription(JoinPoint joinPoint, Log log, OperLogEvent operLog, Object jsonResult) throws Exception {
        // 设置action动作
        operLog.setBusinessType(log.businessType().ordinal());
        // 设置标题
        operLog.setTitle(log.title());

        String requestObj = log.requestObjSpel();
        String operatorObjId = log.operatorObjIdSpel();
        Class<?> tableEntity = log.tableEntity();
        // 设置详情 [用户]在[模块]中[新增等]了[detail]
        operLog.setDetail(StringUtils.substring(getDetail(joinPoint, log, operLog, jsonResult), 0, 2000));

        // 设置操作人类别
        operLog.setOperatorType(log.operatorType().ordinal());
        // 是否需要保存request，参数和值
        if (log.isSaveRequestData()) {
            // 获取参数的信息，传入到数据库中。
            setRequestValue(joinPoint, operLog, log.excludeParamNames());
        }
        // 是否需要保存response，参数和值
        if (log.isSaveResponseData() && ObjectUtil.isNotNull(jsonResult)) {
            operLog.setJsonResult(StringUtils.substring(JsonUtils.toJsonString(jsonResult), 0, 3800));
        }
    }

    /**
     * 获取操作日志详情
     *
     * @param joinPoint 切点对象，用于获取方法参数等信息
     * @param log 日志对象，包含日志的配置信息
     * @param operLog 操作日志事件对象，包含操作的相关信息
     * @return 操作日志的详情内容
     * @throws IllegalAccessException 如果无法访问目标方法时抛出
     * @throws InvocationTargetException 如果目标方法抛出异常时抛出
     * @throws NoSuchMethodException 如果找不到目标方法时抛出
     */
    private String getDetail(JoinPoint joinPoint, Log log, OperLogEvent operLog, Object jsonResult) throws IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        String detailExpression = log.detail();
        String bussinessType = getBussinessType(operLog.getBusinessType());

        // 判断是否使用detail
        if (StringUtils.isNotEmpty(detailExpression)) {
            // 使用detail生成日志内容
            Object[] args = joinPoint.getArgs();
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            String[] paramNames = methodSignature.getParameterNames();

            ExpressionParser parser = new SpelExpressionParser();
            StandardEvaluationContext context = new StandardEvaluationContext();

            for (int i = 0; i < args.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }

            Pattern pattern = Pattern.compile("\\{([^}]+)\\}");
            Matcher matcher = pattern.matcher(detailExpression);

            while (matcher.find()) {
                String expr = matcher.group(1);
                String value = parser.parseExpression(expr).getValue(context, String.class);
                if (value != null) {
                    detailExpression = detailExpression.replaceFirst(Pattern.quote("{" + expr + "}"), value);
                }
            }

            return "[" + operLog.getOperName() + operLog.getOperNickName() + "]" + "在[" + operLog.getTitle() + "]中" + "进行了[" + bussinessType + "]操作：" + detailExpression;
        } else {
            // 如果不包含detail，则使用策略类生成日志内容
            setOperationData(joinPoint, log, jsonResult);
            // 设置操作类型
            OperatorLogContext.setBusinessType(log.businessType());
            // 设置实际策略类
            IOperateLogStrategy strategy = getStrategy(operLog.getBusinessType());
            String detailContent = strategy.getOperatorContent(
                getRequestObj(joinPoint, log.requestObjSpel(), jsonResult),
                getEntityClass(log),
                getOperatorObjId(joinPoint, log.operatorObjIdSpel(), jsonResult)
            );

            return "[" + operLog.getOperName() + operLog.getOperNickName() + "]" + "在[" + operLog.getTitle() + "]中" + detailContent;
        }
    }

    /**
     * 根据给定的业务类型编号获取对应的业务类型名称
     *
     * @param type 业务类型编号
     * @return 对应的业务类型名称
     */
    private String getBussinessType(int type) {
        switch (type) {
            case 0: return "其他";
            case 1: return "新增";
            case 2: return "修改";
            case 3: return "删除";
            case 4: return "查询";
            case 5: return "授权";
            case 6: return "导出";
            case 7: return "导入";
            case 8: return "强退";
            case 9: return "生成代码";
            case 10: return "清空数据";
            case 11: return "文件下载";
            case 12: return "流程启动";
            case 13: return "任务办理";
            case 14: return "流程终止";
            case 15: return "流程驳回";
            case 16: return "任务委派";
            case 17: return "流程挂起";
            default: return "未知";
        }
    }


    /**
     * 设置操作数据，用于记录操作日志时使用
     *
     * @param joinPoint 切点信息
     * @param log 日志信息
     */
    private void setOperationData(JoinPoint joinPoint, Log log, Object jsonResult) {
        if (log.businessType() == BusinessType.INSERT) {
            Object requestObj = getRequestObj(joinPoint, log.requestObjSpel(), jsonResult);
            OperatorLogContext.setNewData(requestObj); // 设置新写入的操作数据
        } else if (log.businessType() == BusinessType.DELETE) {
            OperatorLogContext.setNewData(null); // 删除的新数据设置为空
        } else {
            String operatorObjId = getOperatorObjId(joinPoint, log.operatorObjIdSpel(), jsonResult);
            if (StringUtils.isNotEmpty(operatorObjId)) {
                Object newData = OperatorLogQueryUtil.getObj(operatorObjId, log.tableEntity());
                OperatorLogContext.setNewData(newData); // 设置修改后的操作数据
            }
        }


    }

    /**
     * 根据业务类型获取相应的操作日志策略对象
     *
     * @param businessType 业务类型，1表示新增，2表示修改，3表示删除，其他值表示默认
     * @return 返回对应的操作日志策略对象
     */
    private IOperateLogStrategy getStrategy(int businessType) {
        switch (businessType) {
            case 1: // 新增
                return SpringUtils.getBean(AddOperatorLogStrategy.class);
            case 2: // 修改
                return SpringUtils.getBean(UpdateOperatorLogStrategy.class);
            case 3: // 删除
                return SpringUtils.getBean(DeleteOperatorLogStrategy.class);
            case 4: // 查询
                return SpringUtils.getBean(SearchOperatorLogStrategy.class);
            case 5: // 授权
                return SpringUtils.getBean(GrantOperatorLogStrategy.class);
            case 6: // 导出
                return SpringUtils.getBean(ExportOperatorLogStrategy.class);
            case 7: // 导入
                return SpringUtils.getBean(ImportOperatorLogStrategy.class);
            case 8: // 强退
                return SpringUtils.getBean(ForceOperatorLogStrategy.class);
            // case 9: // 生成代码
            //     return SpringUtils.getBean(GenCodeOperatorLogStrategy.class);
            // case 10: // 清空数据
            //     return SpringUtils.getBean(CleanOperatorLogStrategy.class);
            case 11: // 文件下载
                return SpringUtils.getBean(DownloadOperatorLogStrategy.class);
            case 12:
                return SpringUtils.getBean(WorkflowLogStrategy.class);
            case 13:
                return SpringUtils.getBean(WorkflowLogStrategy.class);
            case 14:
                return SpringUtils.getBean(WorkflowLogStrategy.class);
            case 15:
                return SpringUtils.getBean(WorkflowLogStrategy.class);
            case 16:
                return SpringUtils.getBean(WorkflowLogStrategy.class);
            case 17:
                return SpringUtils.getBean(WorkflowLogStrategy.class);
            default: // 其他
                return SpringUtils.getBean(DefaultOperatorLogStrategy.class);
        }
    }

    /**
     * 获取请求对象，针对spel表达式进行解析获取
     *
     * @param joinPoint 切点对象
     * @param spel        SpEL表达式
     * @return 请求对象
     */
    private Object getRequestObj(JoinPoint joinPoint, String spel, Object jsonResult) {
        if (StringUtils.isEmpty(spel)) {
            return null;
        }
        return parseSpel(joinPoint, spel, jsonResult);
    }

    /**
     * 根据日志获取实体类数组
     *
     * @param log 日志对象
     * @return 实体类数组，如果日志的业务类型为OTHER，则返回空数组；否则返回包含日志表实体类的数组
     */
    private Class<?>[] getEntityClass(Log log) {
        return log.businessType().equals(BusinessType.OTHER) ?
            new Class<?>[0] :
            new Class<?>[]{log.tableEntity()};
    }

    /**
     * 获取操作对象的ID
     *
     * @param joinPoint 切面对象
     * @param spel SpEL表达式
     * @return 操作对象的ID
     */
    private String getOperatorObjId(JoinPoint joinPoint, String spel, Object jsonResult) {
        if (StringUtils.isEmpty(spel)) {
            return null;
        }

        Object value = parseSpel(joinPoint, spel, jsonResult);
        if (value == null) {
            return null;
        }

        // 处理数组类型
        if (value.getClass().isArray()) {
            if (value instanceof Long[]) {
                return Arrays.toString((Long[]) value);
            }
            return Arrays.toString((Object[]) value);
        }

        return String.valueOf(value);
    }



    /**
     * 解析Spring Expression Language（SPEL）表达式。
     *
     * @param joinPoint 切点信息
     * @param spel      需要解析的SPEL表达式
     * @return 解析后的结果对象，如果解析失败则返回null
     */
    private Object parseSpel(JoinPoint joinPoint, String spel, Object jsonResult) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = methodSignature.getParameterNames();
        Object[] args = joinPoint.getArgs();

        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext context = new StandardEvaluationContext();

        // 将方法参数放入上下文
        for (int i = 0; i < args.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }

        // 添加返回值到上下文
        if (jsonResult instanceof R) {
            context.setVariable("result", ((R<?>) jsonResult).getData());
        }

        try {
            return parser.parseExpression(spel).getValue(context);
        } catch (Exception e) {
            log.error("解析SPEL表达式失败: {}", e.getMessage());
            return null;
        }
    }



    /**
     * 获取请求的参数，放到log中
     *
     * @param operLog 操作日志
     * @throws Exception 异常
     */
    private void setRequestValue(JoinPoint joinPoint, OperLogEvent operLog, String[] excludeParamNames) throws Exception {
        Map<String, String> paramsMap = ServletUtils.getParamMap(ServletUtils.getRequest());
        String requestMethod = operLog.getRequestMethod();
        if (MapUtil.isEmpty(paramsMap) && StringUtils.equalsAny(requestMethod, HttpMethod.PUT.name(), HttpMethod.POST.name(), HttpMethod.DELETE.name())) {
            String params = argsArrayToString(joinPoint.getArgs(), excludeParamNames);
            operLog.setOperParam(StringUtils.substring(params, 0, 3800));
        } else {
            MapUtil.removeAny(paramsMap, EXCLUDE_PROPERTIES);
            MapUtil.removeAny(paramsMap, excludeParamNames);
            operLog.setOperParam(StringUtils.substring(JsonUtils.toJsonString(paramsMap), 0, 3800));
        }
    }

    /**
     * 将对象数组转换为字符串形式
     *
     * @param paramsArray 待转换的对象数组
     * @param excludeParamNames 需要排除的属性名数组
     * @return 转换后的字符串
     */
    private String argsArrayToString(Object[] paramsArray, String[] excludeParamNames) {
        StringJoiner params = new StringJoiner(" ");
        if (ArrayUtil.isEmpty(paramsArray)) {
            return params.toString();
        }
        for (Object o : paramsArray) {
            if (ObjectUtil.isNotNull(o) && !isFilterObject(o)) {
                String str = JsonUtils.toJsonString(o);
                Dict dict = JsonUtils.parseMap(str);
                if (MapUtil.isNotEmpty(dict)) {
                    MapUtil.removeAny(dict, EXCLUDE_PROPERTIES);
                    MapUtil.removeAny(dict, excludeParamNames);
                    str = JsonUtils.toJsonString(dict);
                }
                params.add(str);
            }
        }
        return params.toString();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return MultipartFile.class.isAssignableFrom(clazz.getComponentType());
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection collection = (Collection) o;
            for (Object value : collection) {
                return value instanceof MultipartFile;
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map map = (Map) o;
            for (Object value : map.values()) {
                return value instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
            || o instanceof BindingResult;
    }
}


