package org.dromara.cssrcSoftware.domain.bo;

import org.dromara.cssrcSoftware.domain.SoftwareWhiteuser;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 软件权限白名单业务对象 software_whiteuser
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SoftwareWhiteuser.class, reverseConvertGenerate = false)
public class SoftwareWhiteuserBo extends BaseEntity {

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 软件ID
     */
    @NotNull(message = "软件ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long softwareId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 授权原因
     */
    private String grantReason;

    /**
     * 授权时间
     */
    private Date grantTime;

    /**
     * 过期时间（可选）
     */
    private Date expireTime;


}
