package ${packageName}.domain.bo;

import ${packageName}.domain.${ClassName};
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
#foreach ($import in $importList)
import ${import};
#end

/**
 * ${functionName}业务对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ${ClassName}.class, reverseConvertGenerate = false)
public class ${ClassName}Bo extends BaseEntity {

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField) && ($column.query || $column.insert || $column.edit))
    /**
     * $column.columnComment
     */
#if($column.insert && $column.edit)
#set($Group="AddGroup.class, EditGroup.class")
#elseif($column.insert)
#set($Group="AddGroup.class")
#elseif($column.edit)
#set($Group="EditGroup.class")
#end
#if($column.required)
#if($column.javaType == 'String')
    @NotBlank(message = "$column.columnComment不能为空", groups = { $Group })
#else
    @NotNull(message = "$column.columnComment不能为空", groups = { $Group })
#end
#end
    private $column.javaType $column.javaField;

#end
#end

}
