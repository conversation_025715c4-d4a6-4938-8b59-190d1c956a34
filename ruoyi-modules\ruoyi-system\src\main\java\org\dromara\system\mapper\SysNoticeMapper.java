package org.dromara.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.dromara.common.encrypt.annotation.EncryptField;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.system.domain.SysNotice;
import org.dromara.system.domain.SysRole;
import org.dromara.system.domain.vo.SysNoticeVo;

import java.sql.Wrapper;

/**
 * 通知公告表 数据层
 *
 * <AUTHOR> Li
 */
public interface SysNoticeMapper extends BaseMapperPlus<SysNotice, SysNoticeVo> {
    /**
     * 通知公告浏览量自增
     *
     * @param noticeId         通知公告ID
     * @return 包含角色信息的分页结果
     */
    int increaseViewCount(Long noticeId);

    /**
     * 通知公告列表（后台管理页面）限制了数据权限
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "n.create_dept"), // 部门关联字段
        @DataColumn(key = "userName", value = "n.create_by") // 用户关联字段
    })
    Page<SysNoticeVo> selectVoPageWithPermission(@Param("page") Page<SysNotice> page, @Param(Constants.WRAPPER) LambdaQueryWrapper<SysNotice> queryWrapper);


}
