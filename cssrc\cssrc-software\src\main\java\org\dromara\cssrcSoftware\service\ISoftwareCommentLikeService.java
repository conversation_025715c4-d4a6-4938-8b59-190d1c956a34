package org.dromara.cssrcSoftware.service;

import org.dromara.cssrcSoftware.domain.vo.SoftwareCommentLikeVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareCommentLikeBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 软件点赞关联Service接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface ISoftwareCommentLikeService {

    /**
     * 查询软件点赞关联
     *
     * @param id 主键
     * @return 软件点赞关联
     */
    SoftwareCommentLikeVo queryById(Long id);

    /**
     * 分页查询软件点赞关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 软件点赞关联分页列表
     */
    TableDataInfo<SoftwareCommentLikeVo> queryPageList(SoftwareCommentLikeBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的软件点赞关联列表
     *
     * @param bo 查询条件
     * @return 软件点赞关联列表
     */
    List<SoftwareCommentLikeVo> queryList(SoftwareCommentLikeBo bo);

    /**
     * 新增软件点赞关联
     *
     * @param bo 软件点赞关联
     * @return 是否新增成功
     */
    Boolean insertByBo(SoftwareCommentLikeBo bo);

    /**
     * 修改软件点赞关联
     *
     * @param bo 软件点赞关联
     * @return 是否修改成功
     */
    Boolean updateByBo(SoftwareCommentLikeBo bo);

    /**
     * 校验并批量删除软件点赞关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
