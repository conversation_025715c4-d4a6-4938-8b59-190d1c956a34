package org.dromara.cssrcSoftware.controller;

import java.util.List;

import cn.hutool.core.lang.tree.Tree;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.cssrcSoftware.domain.SoftwareCategory;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.cssrcSoftware.domain.vo.SoftwareCategoryVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareCategoryBo;
import org.dromara.cssrcSoftware.service.ISoftwareCategoryService;

/**
 * 软件分类
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("cssrc/cssrcSoftwareCategory")
public class SoftwareCategoryController extends BaseController {

    private final ISoftwareCategoryService softwareCategoryService;

    /**
     * 查询软件分类列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareCategory:list")
    @GetMapping("/list")
    public R<List<SoftwareCategoryVo>> list(SoftwareCategoryBo bo) {
        List<SoftwareCategoryVo> list = softwareCategoryService.queryList(bo);
        return R.ok(list);
    }

    @SaCheckPermission("cssrc:cssrcSoftwareInfo:list")
    @GetMapping("/categoryTree")
    public R<List<Tree<Long>>> categoryTree(SoftwareCategoryBo bo) {
        return R.ok(softwareCategoryService.selectCategoryTreeList(bo));
    }

    /**
     * 导出软件分类列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareCategory:export")
    @Log(title = "软件分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SoftwareCategoryBo bo, HttpServletResponse response) {
        List<SoftwareCategoryVo> list = softwareCategoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "软件分类", SoftwareCategoryVo.class, response);
    }

    /**
     * 获取软件分类详细信息
     *
     * @param categoryId 主键
     */
    @SaCheckPermission("cssrc:cssrcSoftwareCategory:query")
    @GetMapping("/{categoryId}")
    public R<SoftwareCategoryVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long categoryId) {
        return R.ok(softwareCategoryService.queryById(categoryId));
    }
    /**
     * 新增软件分类
     */
    @SaCheckPermission("cssrc:cssrcSoftwareCategory:add")
    @Log(
        title = "软件分类",
        businessType = BusinessType.INSERT,
        requestObjSpel = "#bo",
        tableEntity = SoftwareCategory.class
    )
    @RepeatSubmit()
    @PostMapping()
    public R<SoftwareCategoryVo> add(@Validated(AddGroup.class) @RequestBody SoftwareCategoryBo bo) {
        if (!softwareCategoryService.checkSoftwareCategoryUnique(bo)) {
            return R.fail("新增软件分类'" + bo.getCategoryName() + "'失败，软件分类名称已存在");
        }
        return R.ok(softwareCategoryService.insertByBo(bo));
    }

    /**
     * 修改软件分类
     */
    @SaCheckPermission("cssrc:cssrcSoftwareCategory:edit")
    @Log(title = "软件分类", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<SoftwareCategoryVo> edit(@Validated(EditGroup.class) @RequestBody SoftwareCategoryBo bo) {
        if (!softwareCategoryService.checkSoftwareCategoryUnique(bo)) {
            return R.fail("修改软件分类'" + bo.getCategoryName() + "'失败，软件分类名称已存在");
        } else if (bo.getParentId().equals(bo.getCategoryId())) {
            return R.fail("修改软件分类'" + bo.getCategoryName() + "'失败，上级软件分类不能是自己");
        }
        return R.ok(softwareCategoryService.updateByBo(bo));
    }

    /**
     * 删除软件分类
     *
     * @param categoryIds 主键串
     */
    @SaCheckPermission("cssrc:cssrcSoftwareCategory:remove")
    @Log(title = "软件分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{categoryIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] categoryIds) {
        for (Long categoryId : categoryIds) {
            if (softwareCategoryService.hasChildByCategoryId(categoryId)) {
                return R.warn("存在下级软件分类,不允许删除");
            }
        }

        return toAjax(softwareCategoryService.deleteWithValidByIds(List.of(categoryIds), true));
    }
}
