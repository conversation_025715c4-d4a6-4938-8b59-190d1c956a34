package org.dromara.cssrcSoftware.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 软件入网申请对象 software_access
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("software_access")
public class SoftwareAccess extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 申请id
     */
    @TableId(value = "access_id")
    private Long accessId;

    /**
     * 入网类型
     */
    private String accessType;

    /**
     * 入网申请
     */
    private String accessKind;

    /**
     * 责任人
     */
    private Long accessResponsible;

    /**
     * 计算机联网类型
     */
    private String networkType;

    /**
     * 计算机密级编号
     */
    private String computerCode;

    /**
     * 软件分类ID
     */
    @TableField(value = "category_id", updateStrategy = FieldStrategy.ALWAYS)
    private Long categoryId;

    /**
     * 软件ID
     */
    @TableField(value = "software_id", updateStrategy = FieldStrategy.ALWAYS)
    private Long softwareId;

    /**
     * 软件名称
     */
    private String softwareName;

    /**
     * 软件密级
     */
    private String secret;

    /**
     * 生产厂商
     */
    private String manufacturer;

    /**
     * 生产国别
     */
    private String country;

    /**
     * 软件简介
     */
    private String intro;

    /**
     * 软件版本ID
     */
    @TableField(value = "version_id", updateStrategy = FieldStrategy.ALWAYS)
    private Long versionId;

    /**
     * 版本号（如 v1.0.0）
     */
    private String versionName;

    /**
     * 平台（windows/linux）
     */
    private String platform;

    /**
     * 架构（x86/x64/arm64）
     */
    private String architecture;

    /**
     * 包类型（exe/msi/deb/rpm/tar.gz）
     */
    private String packageType;

    /**
     * 位数（32/64）
     */
    private String bits;

    /**
     * 文件id
     */

    private Long ossId;

    /**
     * 文件大小(GB)
     */
    private Long fileSize;

    /**
     * 版本备注
     */
    private String remark;

    /**
     * 流程状态
     */
    private String flowStatus;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;


}
