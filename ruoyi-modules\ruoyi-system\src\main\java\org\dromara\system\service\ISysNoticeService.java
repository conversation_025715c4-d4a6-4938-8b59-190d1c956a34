package org.dromara.system.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.bo.SysNoticeBo;
import org.dromara.system.domain.vo.SysNoticeVo;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 公告 服务层
 *
 * <AUTHOR> Li
 */
public interface ISysNoticeService {


    TableDataInfo<SysNoticeVo> selectPageNoticeList(SysNoticeBo notice, PageQuery pageQuery);

    TableDataInfo<SysNoticeVo> selectPageNoticeManagementList(SysNoticeBo notice, PageQuery pageQuery);

    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    SysNoticeVo selectNoticeById(Long noticeId);

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    List<SysNoticeVo> selectNoticeList(SysNoticeBo notice);

    /**
     * 新增公告
     *
     * @param bo 公告信息
     * @return 结果
     */
    SysNoticeVo insertNotice(SysNoticeBo bo);

    /**
     * 修改公告
     *
     * @param bo 公告信息
     * @return 结果
     */
    SysNoticeVo updateNotice(SysNoticeBo bo);

    /**
     * 删除公告信息
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    int deleteNoticeById(Long noticeId);

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    int deleteNoticeByIds(List<Long> noticeIds);

    /**
     * 关联附件下载次数计数+1
     *
     * @param noticeId 公告ID
     * @param ossId 附件ID
     */
    int increaseNoticeOssDownloadCount(Long noticeId, Long ossId);

    /**
     * 删除通知公告和文件的关联关系
     * @param noticeId
     * @param ossIds
     * @return
     */
    Boolean deleteNoticeOssRelation(Long noticeId, List<Long> ossIds);

    /**
     * 初始化通知公告的布隆过滤器
     */
    void initBloomFilter();

    /**
     * 标记通知公告为已读
     */
    void markAsRead(Long noticeId);
}
