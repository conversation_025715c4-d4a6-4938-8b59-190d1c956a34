package org.dromara.common.log.strategy;

import cn.hutool.core.convert.Convert;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.log.context.WorkflowLogContext;
import org.dromara.common.log.utils.OperatorLogContext;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class WorkflowLogStrategy implements IOperateLogStrategy {
    @Override
    public String getOperatorContent(Object requestObj, Class<?>[] tableEntity, String operatorObjId) {
        Map<String, Object> context = WorkflowLogContext.getContext();
        if(context == null) {
            return null;
        }

        String processName = Convert.toStr(context.get("processName"));
        String nodeName = Convert.toStr(context.get("nodeName"));
        String message = Convert.toStr(context.get("message"));

//        // 使用Map接收requestObj,避免直接依赖Bo类
//        Map<String, Object> paramMap = JsonUtils.parseMap(JsonUtils.toJsonString(requestObj));

        // 根据业务类型生成不同的日志内容
        switch(OperatorLogContext.getBusinessType()) {
            case WORKFLOW_START:
                return String.format("发起了流程[%s]启动了[%s]", processName, nodeName);

            case WORKFLOW_COMPLETE:
                return String.format("在流程[%s]完成了[%s]节点,处理意见:%s",
                    processName, nodeName, message);

            case WORKFLOW_TERMINATE:
                return String.format("终止了流程[%s]的任务[%s]", processName, nodeName);

            case WORKFLOW_BACK:
                return String.format("驳回了流程[%s]的[%s]节点任务，处理意见[%s]",
                    processName, nodeName, message);

            case WORKFLOW_DELEGATE:
                return String.format("在流程[%s]中将任务[%s]委派给了其他人",
                    processName, nodeName);

            default:
                return null;
        }
    }

}

