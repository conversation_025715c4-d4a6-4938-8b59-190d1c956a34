package org.dromara.cssrcSoftware.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.cssrcSoftware.domain.vo.SoftwareDownloadLogVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareDownloadLogBo;
import org.dromara.cssrcSoftware.service.ISoftwareDownloadLogService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 软件下载日志
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/cssrc/cssrcSoftwareDownloadLog")
public class SoftwareDownloadLogController extends BaseController {

    private final ISoftwareDownloadLogService softwareDownloadLogService;

    /**
     * 查询软件下载日志列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareDownloadLog:list")
    @GetMapping("/list")
    public TableDataInfo<SoftwareDownloadLogVo> list(SoftwareDownloadLogBo bo, PageQuery pageQuery) {
        return softwareDownloadLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出软件下载日志列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareDownloadLog:export")
    @Log(title = "软件下载日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SoftwareDownloadLogBo bo, HttpServletResponse response) {
        List<SoftwareDownloadLogVo> list = softwareDownloadLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "软件下载日志", SoftwareDownloadLogVo.class, response);
    }

    /**
     * 获取软件下载日志详细信息
     *
     * @param logId 主键
     */
    @SaCheckPermission("cssrc:cssrcSoftwareDownloadLog:query")
    @GetMapping("/{logId}")
    public R<SoftwareDownloadLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long logId) {
        return R.ok(softwareDownloadLogService.queryById(logId));
    }

    /**
     * 新增软件下载日志
     */
    @SaCheckPermission("cssrc:cssrcSoftwareDownloadLog:add")
    @Log(title = "软件下载日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SoftwareDownloadLogBo bo) {
        return toAjax(softwareDownloadLogService.insertByBo(bo));
    }

    /**
     * 修改软件下载日志
     */
    @SaCheckPermission("cssrc:cssrcSoftwareDownloadLog:edit")
    @Log(title = "软件下载日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SoftwareDownloadLogBo bo) {
        return toAjax(softwareDownloadLogService.updateByBo(bo));
    }

    /**
     * 删除软件下载日志
     *
     * @param logIds 主键串
     */
    @SaCheckPermission("cssrc:cssrcSoftwareDownloadLog:remove")
    @Log(title = "软件下载日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] logIds) {
        return toAjax(softwareDownloadLogService.deleteWithValidByIds(List.of(logIds), true));
    }
}
