package org.dromara.cssrcSoftware.service.impl;

import cn.hutool.core.util.ObjectUtil;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StreamUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.cssrcSoftware.domain.SoftwareCategory;
import org.dromara.cssrcSoftware.domain.vo.SoftwareCategoryVo;
import org.dromara.cssrcSoftware.mapper.SoftwareCategoryMapper;
import org.dromara.cssrcSoftware.service.ISoftwareCategoryService;
import org.springframework.stereotype.Service;
import org.dromara.cssrcSoftware.domain.bo.SoftwareInfoBo;
import org.dromara.cssrcSoftware.domain.vo.SoftwareInfoVo;
import org.dromara.cssrcSoftware.domain.SoftwareInfo;
import org.dromara.cssrcSoftware.mapper.SoftwareInfoMapper;
import org.dromara.cssrcSoftware.service.ISoftwareInfoService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 软件基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RequiredArgsConstructor
@Service
public class SoftwareInfoServiceImpl implements ISoftwareInfoService {

    private final SoftwareInfoMapper baseMapper;
    private final SoftwareCategoryMapper categoryMapper;
    private final ISoftwareCategoryService softwareCategoryService;

    /**
     * 查询软件基本信息
     *
     * @param softwareId 主键
     * @return 软件基本信息
     */
    @Override
    public SoftwareInfoVo queryById(Long softwareId){
        SoftwareInfoVo vo = baseMapper.selectVoById(softwareId);
        SoftwareCategoryVo categoryVo = softwareCategoryService.queryById(vo.getCategoryId());
        vo.setCategoryName(categoryVo.getCategoryName());
        return vo;
    }

    /**
     * 分页查询软件基本信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 软件基本信息分页列表
     */
    @Override
    public TableDataInfo<SoftwareInfoVo> queryPageList(SoftwareInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SoftwareInfo> lqw = buildQueryWrapper(bo);
        Page<SoftwareInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的软件基本信息列表
     *
     * @param bo 查询条件
     * @return 软件基本信息列表
     */
    @Override
    public List<SoftwareInfoVo> queryList(SoftwareInfoBo bo) {
        LambdaQueryWrapper<SoftwareInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SoftwareInfo> buildQueryWrapper(SoftwareInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SoftwareInfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SoftwareInfo::getSoftwareId);
//        lqw.eq(bo.getCategoryId() != null, SoftwareInfo::getCategoryId, bo.getCategoryId());
        lqw.like(StringUtils.isNotBlank(bo.getSoftwareName()), SoftwareInfo::getSoftwareName, bo.getSoftwareName());
        lqw.eq(StringUtils.isNotBlank(bo.getManufacturer()), SoftwareInfo::getManufacturer, bo.getManufacturer());
        lqw.eq(StringUtils.isNotBlank(bo.getCountry()), SoftwareInfo::getCountry, bo.getCountry());
        lqw.eq(StringUtils.isNotBlank(bo.getIntro()), SoftwareInfo::getIntro, bo.getIntro());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SoftwareInfo::getStatus, bo.getStatus());
        lqw.eq(bo.getDownloadCount() != null, SoftwareInfo::getDownloadCount, bo.getDownloadCount());

        // 针对软件分类特殊处理
        lqw.and(ObjectUtil.isNotNull(bo.getCategoryId()), w -> {
            List<SoftwareCategory> deptList = categoryMapper.selectListByParentId(bo.getCategoryId());
            List<Long> ids = StreamUtils.toList(deptList, SoftwareCategory::getCategoryId);
            ids.add(bo.getCategoryId());
            w.in(SoftwareInfo::getCategoryId, ids);
        }).orderByDesc(SoftwareInfo::getSoftwareId);
        return lqw;
    }

    /**
     * 新增软件基本信息
     *
     * @param bo 软件基本信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SoftwareInfoBo bo) {
        SoftwareInfo add = MapstructUtils.convert(bo, SoftwareInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setSoftwareId(add.getSoftwareId());
        }
        return flag;
    }

    /**
     * 修改软件基本信息
     *
     * @param bo 软件基本信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SoftwareInfoBo bo) {
        SoftwareInfo update = MapstructUtils.convert(bo, SoftwareInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SoftwareInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除软件基本信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
