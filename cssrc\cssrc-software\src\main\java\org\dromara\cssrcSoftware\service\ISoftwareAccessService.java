package org.dromara.cssrcSoftware.service;

import org.dromara.cssrcSoftware.domain.vo.SoftwareAccessVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareAccessBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 软件入网申请Service接口
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface ISoftwareAccessService {

    /**
     * 查询软件入网申请
     *
     * @param accessId 主键
     * @return 软件入网申请
     */
    SoftwareAccessVo queryById(Long accessId);

    /**
     * 分页查询软件入网申请列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 软件入网申请分页列表
     */
    TableDataInfo<SoftwareAccessVo> queryPageList(SoftwareAccessBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的软件入网申请列表
     *
     * @param bo 查询条件
     * @return 软件入网申请列表
     */
    List<SoftwareAccessVo> queryList(SoftwareAccessBo bo);

    /**
     * 新增软件入网申请
     *
     * @param bo 软件入网申请
     * @return 是否新增成功
     */
    Boolean insertByBo(SoftwareAccessBo bo);

    /**
     * 修改软件入网申请
     *
     * @param bo 软件入网申请
     * @return 是否修改成功
     */
    Boolean updateByBo(SoftwareAccessBo bo);

    /**
     * 校验并批量删除软件入网申请信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
