package org.dromara.system.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.BooleanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.constant.TenantConstants;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.ip.AddressUtils;
import org.dromara.common.encrypt.utils.EncryptUtils;
import org.dromara.common.log.event.OperLogEvent;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.SysOperLog;
import org.dromara.system.domain.bo.SysOperLogBo;
import org.dromara.system.domain.bo.SysRoleBo;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.domain.vo.SysOperLogVo;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.mapper.SysOperLogMapper;
import org.dromara.system.service.ISysOperLogService;
import org.dromara.system.service.ISysRoleService;
import org.dromara.system.service.ISysUserService;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 操作日志 服务层处理
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
public class SysOperLogServiceImpl implements ISysOperLogService {

    private final SysOperLogMapper baseMapper;
    private final ISysRoleService roleService;
    private final ISysUserService userService;

    /**
     * 操作日志记录
     *
     * @param operLogEvent 操作日志事件
     */
    @Async
    @EventListener
    public void recordOper(OperLogEvent operLogEvent) {
        SysOperLogBo operLog = MapstructUtils.convert(operLogEvent, SysOperLogBo.class);
        // 远程查询操作地点
        operLog.setOperLocation(AddressUtils.getRealAddressByIP(operLog.getOperIp()));
        insertOperlog(operLog);
    }

    @Override
    public TableDataInfo<SysOperLogVo> selectPageOperLogList(SysOperLogBo operLog, PageQuery pageQuery) {
        LambdaQueryWrapper<SysOperLog> lqw = buildQueryWrapper(operLog);
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            lqw.orderByDesc(SysOperLog::getOperId);
        }
        Page<SysOperLogVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    private LambdaQueryWrapper<SysOperLog> buildQueryWrapper(SysOperLogBo operLog) {
        Map<String, Object> params = operLog.getParams();
        LambdaQueryWrapper<SysOperLog> lqw = new LambdaQueryWrapper<SysOperLog>();

        // 获取当前用户角色
        List<SysRoleVo> userRoles = roleService.selectRolesByUserId(LoginHelper.getUserId());

        // 应用角色过滤
        if (userRoles.stream().anyMatch(role -> TenantConstants.RIGHT_ROLE_KEY.equals(role.getRoleKey()))) {
            // right角色 - 排除right和system角色的日志
            List<String> excludeUserNames = roleService.getUserNamesByRoles(Arrays.asList(TenantConstants.RIGHT_ROLE_KEY, TenantConstants.SYSTEM_ROLE_KEY));
            if (!excludeUserNames.isEmpty()) {
                lqw.notIn(SysOperLog::getOperName, excludeUserNames);
            }
        } else if (userRoles.stream().anyMatch(role -> TenantConstants.CHECK_ROLE_KEY.equals(role.getRoleKey()))) {
            // check角色 - 只显示right和system角色的日志
            List<String> includeUserNames = roleService.getUserNamesByRoles(Arrays.asList(TenantConstants.RIGHT_ROLE_KEY, TenantConstants.SYSTEM_ROLE_KEY));
            if (!includeUserNames.isEmpty()) {
                lqw.in(SysOperLog::getOperName, includeUserNames);
            } else {
                lqw.eq(SysOperLog::getOperName, "-1");  // 使用一个不存在的用户名确保查询为空
            }
        }

        // 应用现有搜索条件
        lqw.like(StringUtils.isNotBlank(operLog.getOperIp()), SysOperLog::getOperIp, operLog.getOperIp())
            .like(StringUtils.isNotBlank(operLog.getTitle()), SysOperLog::getTitle, operLog.getTitle())
            .eq(operLog.getBusinessType() != null && operLog.getBusinessType() > 0,
                SysOperLog::getBusinessType, operLog.getBusinessType())
            .func(f -> {
                if (ArrayUtil.isNotEmpty(operLog.getBusinessTypes())) {
                    f.in(SysOperLog::getBusinessType, Arrays.asList(operLog.getBusinessTypes()));
                }
            })
            .eq(operLog.getStatus() != null, SysOperLog::getStatus, operLog.getStatus())
            .like(StringUtils.isNotBlank(operLog.getOperName()), SysOperLog::getOperName, operLog.getOperName())
            .like(StringUtils.isNotBlank(operLog.getOperNickName()), SysOperLog::getOperNickName, operLog.getOperNickName())
            .between(params.get("beginTime") != null && params.get("endTime") != null,
                SysOperLog::getOperTime, params.get("beginTime"), params.get("endTime"))
            .like(StringUtils.isNotBlank(operLog.getDetail()), SysOperLog::getDetail, operLog.getDetail())
            .func(f -> {
                if (BooleanUtil.toBoolean(SpringUtils.getProperty("mybatis-encryptor.enable"))) {
                    f.eq(StringUtils.isNotBlank(operLog.getSecret()), SysOperLog::getSecret, EncryptUtils.encryptByBase64(operLog.getSecret()));
                }else {
                    f.eq(StringUtils.isNotBlank(operLog.getSecret()), SysOperLog::getSecret, operLog.getSecret());
                }
            });

        return lqw;
    }



    /**
     * 新增操作日志
     *
     * @param bo 操作日志对象
     */
    @Override
    public void insertOperlog(SysOperLogBo bo) {
        SysOperLog operLog = MapstructUtils.convert(bo, SysOperLog.class);
        operLog.setOperTime(new Date());
        baseMapper.insert(operLog);
    }

    /**
     * 查询系统操作日志集合
     *
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    @Override
    public List<SysOperLogVo> selectOperLogList(SysOperLogBo operLog) {
        LambdaQueryWrapper<SysOperLog> lqw = buildQueryWrapper(operLog);
        return baseMapper.selectVoList(lqw.orderByDesc(SysOperLog::getOperId));
    }

    /**
     * 批量删除系统操作日志
     *
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    @Override
    public int deleteOperLogByIds(Long[] operIds) {
        return baseMapper.deleteByIds(Arrays.asList(operIds));
    }

    /**
     * 查询操作日志详细
     *
     * @param operId 操作ID
     * @return 操作日志对象
     */
    @Override
    public SysOperLogVo selectOperLogById(Long operId) {
        return baseMapper.selectVoById(operId);
    }

    /**
     * 清空操作日志
     */
    @Override
    public void cleanOperLog() {
        baseMapper.delete(new LambdaQueryWrapper<>());
    }

    /**
     * 查询操作日志记录
     *
     * @param operId 主键
     * @return 操作日志记录
     */
    @Override
    public SysOperLogVo queryById(Long operId){
        return baseMapper.selectVoById(operId);
    }

    /**
     * 分页查询操作日志记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 操作日志记录分页列表
     */
    @Override
    public TableDataInfo<SysOperLogVo> queryPageList(SysOperLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysOperLog> lqw = buildQueryWrapper(bo);
        Page<SysOperLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的操作日志记录列表
     *
     * @param bo 查询条件
     * @return 操作日志记录列表
     */
    @Override
    public List<SysOperLogVo> queryList(SysOperLogBo bo) {
        LambdaQueryWrapper<SysOperLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增操作日志记录
     *
     * @param bo 操作日志记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SysOperLogBo bo) {
        SysOperLog add = MapstructUtils.convert(bo, SysOperLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setOperId(add.getOperId());
        }
        return flag;
    }

    /**
     * 修改操作日志记录
     *
     * @param bo 操作日志记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SysOperLogBo bo) {
        SysOperLog update = MapstructUtils.convert(bo, SysOperLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysOperLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除操作日志记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
