package org.dromara.cssrcSoftware.controller;

import java.util.List;
import java.util.Date;
import java.io.IOException;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.cssrcSoftware.domain.vo.SoftwareVersionVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareVersionBo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareDownloadLogBo;
import org.dromara.cssrcSoftware.service.ISoftwareVersionService;
import org.dromara.cssrcSoftware.service.ISoftwareDownloadLogService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 软件版本详情
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/cssrc/cssrcSoftwareVersion")
public class SoftwareVersionController extends BaseController {

    private final ISoftwareVersionService softwareVersionService;
    private final ISoftwareDownloadLogService softwareDownloadLogService;

    /**
     * 查询软件版本详情列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareVersion:list")
    @GetMapping("/list")
    public TableDataInfo<SoftwareVersionVo> list(SoftwareVersionBo bo, PageQuery pageQuery) {
        return softwareVersionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出软件版本详情列表
     */
    @SaCheckPermission("cssrc:cssrcSoftwareVersion:export")
    @Log(title = "软件版本详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SoftwareVersionBo bo, HttpServletResponse response) {
        List<SoftwareVersionVo> list = softwareVersionService.queryList(bo);
        ExcelUtil.exportExcel(list, "软件版本详情", SoftwareVersionVo.class, response);
    }

    /**
     * 获取软件版本详情详细信息
     *
     * @param versionId 主键
     */
    @SaCheckPermission("cssrc:cssrcSoftwareVersion:query")
    @GetMapping("/{versionId}")
    public R<SoftwareVersionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long versionId) {
        return R.ok(softwareVersionService.queryById(versionId));
    }

    /**
     * 新增软件版本详情
     */
    @SaCheckPermission("cssrc:cssrcSoftwareVersion:add")
    @Log(title = "软件版本详情", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<SoftwareVersionVo> add(@Validated(AddGroup.class) @RequestBody SoftwareVersionBo bo) {
        return R.ok(softwareVersionService.insertByBo(bo));
    }

    /**
     * 修改软件版本详情
     */
    @SaCheckPermission("cssrc:cssrcSoftwareVersion:edit")
    @Log(title = "软件版本详情", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<SoftwareVersionVo> edit(@Validated(EditGroup.class) @RequestBody SoftwareVersionBo bo) {
        return R.ok(softwareVersionService.updateByBo(bo));
    }

    /**
     * 删除软件版本详情
     *
     * @param versionIds 主键串
     */
    @SaCheckPermission("cssrc:cssrcSoftwareVersion:remove")
    @Log(title = "软件版本详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{versionIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] versionIds) {
        return toAjax(softwareVersionService.deleteWithValidByIds(List.of(versionIds), true));
    }

    // ==================== 用户侧接口（无权限验证） ====================

    /**
     * 用户侧查询软件版本详情列表（无权限验证）
     */
    @GetMapping("/public/list")
    public TableDataInfo<SoftwareVersionVo> publicList(SoftwareVersionBo bo, PageQuery pageQuery) {
        // 只查询正常状态且前台显示的版本
        bo.setStatus("0");
        bo.setDisplayStatus("0");
        bo.setApprovalStatus("finish");
        return softwareVersionService.queryPageList(bo, pageQuery);
    }

    /**
     * 用户侧获取软件版本详情详细信息（无权限验证）
     *
     * @param versionId 主键
     */
    @GetMapping("/public/{versionId}")
    public R<SoftwareVersionVo> publicGetInfo(@NotNull(message = "主键不能为空")
                                             @PathVariable Long versionId) {
        SoftwareVersionVo vo = softwareVersionService.queryById(versionId);
        if (vo == null || !"0".equals(vo.getStatus()) || !"0".equals(vo.getDisplayStatus()) || !"finish".equals(vo.getApprovalStatus())) {
            return R.fail("软件版本不存在或不可用");
        }
        return R.ok(vo);
    }

    /**
     * 用户侧根据软件ID查询版本列表（无权限验证）
     *
     * @param softwareId 软件ID
     */
    @GetMapping("/public/software/{softwareId}")
    public TableDataInfo<SoftwareVersionVo> publicListBySoftware(@PathVariable Long softwareId, PageQuery pageQuery) {
        SoftwareVersionBo bo = new SoftwareVersionBo();
        bo.setSoftwareId(softwareId);
        bo.setStatus("0");
        bo.setDisplayStatus("0");
        bo.setApprovalStatus("finish");
        return softwareVersionService.queryPageList(bo, pageQuery);
    }

    /**
     * 用户侧下载软件版本文件（无权限验证）
     *
     * @param versionId 版本ID
     * @param response HttpServletResponse
     */
    @GetMapping("/public/download/{versionId}")
    public void publicDownload(@PathVariable Long versionId, HttpServletResponse response) {
        try {
            // 获取版本信息
            SoftwareVersionVo version = softwareVersionService.queryById(versionId);
            if (version == null || !"0".equals(version.getStatus()) || !"0".equals(version.getDisplayStatus()) || !"finish".equals(version.getApprovalStatus())) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 记录下载日志
            SoftwareDownloadLogBo logBo = new SoftwareDownloadLogBo();
            logBo.setSoftwareId(version.getSoftwareId());
            logBo.setVersionId(versionId);
            logBo.setUserId(1L); // 默认用户ID，实际应该从登录信息获取
            logBo.setIpAddress(getClientIP());
            logBo.setDownloadTime(new Date());
            logBo.setStatus("success");
            softwareDownloadLogService.insertByBo(logBo);

            // 更新下载次数
            version.setDownloadCount(version.getDownloadCount() + 1);
            SoftwareVersionBo updateBo = new SoftwareVersionBo();
            updateBo.setVersionId(versionId);
            updateBo.setDownloadCount(version.getDownloadCount());
            softwareVersionService.updateByBo(updateBo);

            // 通过OSS ID下载文件
            // 这里需要调用系统的OSS服务来下载文件
            // 由于跨模块调用，暂时返回下载链接
            response.sendRedirect("/resource/oss/download/" + version.getOssId());

        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIP() {
        // 简化实现，实际应该从ServletUtils获取
        return "127.0.0.1";
    }
}
