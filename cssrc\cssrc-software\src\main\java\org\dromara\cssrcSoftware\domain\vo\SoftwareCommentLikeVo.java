package org.dromara.cssrcSoftware.domain.vo;

import org.dromara.cssrcSoftware.domain.SoftwareCommentLike;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 软件点赞关联视图对象 software_comment_like
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SoftwareCommentLike.class)
public class SoftwareCommentLikeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long commentId;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long userId;

    /**
     * 1点赞 2点踩
     */
    @ExcelProperty(value = "1点赞 2点踩")
    private Long likeType;


}
