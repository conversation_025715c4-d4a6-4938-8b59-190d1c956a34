package org.dromara.system.controller.system;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.system.domain.SysStandard;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.system.domain.vo.SysStandardVo;
import org.dromara.system.domain.bo.SysStandardBo;
import org.dromara.system.service.ISysStandardService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 标准
 *
 * <AUTHOR> Li
 * @date 2025-06-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/standard")
public class SysStandardController extends BaseController {

    private final ISysStandardService sysStandardService;

    /**
     * 查询标准列表
     */
    @SaCheckPermission("system:standard:list")
    @GetMapping("/list")
    public TableDataInfo<SysStandardVo> list(SysStandardBo bo, PageQuery pageQuery) {
        return sysStandardService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出标准列表
     */
    @SaCheckPermission("system:standard:export")
    @Log(title = "标准", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysStandardBo bo, HttpServletResponse response) {
        List<SysStandardVo> list = sysStandardService.queryList(bo);
        ExcelUtil.exportExcel(list, "标准", SysStandardVo.class, response);
    }

    /**
     * 获取标准详细信息
     *
     * @param standardId 主键
     */
    @SaCheckPermission("system:standard:query")
    @GetMapping("/{standardId}")
    public R<SysStandardVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long standardId) {
        return R.ok(sysStandardService.queryById(standardId));
    }

    /**
     * 新增标准
     */
    @SaCheckPermission("system:standard:add")
    @Log(
        title = "标准",
        businessType = BusinessType.INSERT,
        requestObjSpel = "#bo",
        tableEntity = SysStandard.class
    )
    @RepeatSubmit()
    @PostMapping()
    public R<SysStandardVo> add(@Validated(AddGroup.class) @RequestBody SysStandardBo bo) {
        return R.ok(sysStandardService.insertByBo(bo));
    }

    /**
     * 修改标准
     */
    @SaCheckPermission("system:standard:edit")
    @Log(
            title = "标准",
            businessType = BusinessType.UPDATE,
            requestObjSpel = "#bo",
            operatorObjIdSpel = "#bo.getProcedureId()",
            tableEntity = SysStandard.class
    )
    @RepeatSubmit()
    @PutMapping()
    public R<SysStandardVo> edit(@Validated(EditGroup.class) @RequestBody SysStandardBo bo) {
        return R.ok(sysStandardService.updateByBo(bo));
    }

    /**
     * 删除标准
     *
     * @param standardIds 主键串
     */
    @SaCheckPermission("system:standard:remove")
    @Log(
        title = "标准",
        businessType = BusinessType.DELETE,
        operatorObjIdSpel = "#standardIds",
        tableEntity = SysStandard.class
    )
    @DeleteMapping("/{standardIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] standardIds) {
        return toAjax(sysStandardService.deleteWithValidByIds(List.of(standardIds), true));
    }
}
