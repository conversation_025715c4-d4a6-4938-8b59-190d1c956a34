package org.dromara.cssrcSoftware.service;

import cn.hutool.core.lang.tree.Tree;
import org.dromara.cssrcSoftware.domain.vo.SoftwareCategoryVo;
import org.dromara.cssrcSoftware.domain.bo.SoftwareCategoryBo;

import java.util.Collection;
import java.util.List;

/**
 * 软件分类Service接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface ISoftwareCategoryService {

    /**
     * 查询软件分类
     *
     * @param categoryId 主键
     * @return 软件分类
     */
    SoftwareCategoryVo queryById(Long categoryId);

    /**
     * 查询软件分类树结构信息
     *
     * @param bo 软件分类信息
     * @return 软件分类信息集合
     */
    List<Tree<Long>> selectCategoryTreeList(SoftwareCategoryBo bo);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param categorys 软件分类列表
     * @return 下拉树结构列表
     */
    List<Tree<Long>> buildCategoryTreeSelect(List<SoftwareCategoryVo> categorys);

    /**
     * 查询符合条件的软件分类列表
     *
     * @param bo 查询条件
     * @return 软件分类列表
     */
    List<SoftwareCategoryVo> queryList(SoftwareCategoryBo bo);

    /**
     * 新增软件分类
     *
     * @param bo 软件分类
     * @return 是否新增成功
     */
    SoftwareCategoryVo insertByBo(SoftwareCategoryBo bo);

    /**
     * 修改软件分类
     *
     * @param bo 软件分类
     * @return 是否修改成功
     */
    SoftwareCategoryVo updateByBo(SoftwareCategoryBo bo);

    /**
     * 校验并批量删除软件分类信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 校验软件分类名称是否唯一
     *
     * @param bo 软件分类信息
     * @return 结果
     */
    boolean checkSoftwareCategoryUnique(SoftwareCategoryBo bo);

    /**
     * 是否存在子节点
     *
     * @param categoryId 软件分类ID
     * @return 结果
     */
    boolean hasChildByCategoryId(Long categoryId);
}
