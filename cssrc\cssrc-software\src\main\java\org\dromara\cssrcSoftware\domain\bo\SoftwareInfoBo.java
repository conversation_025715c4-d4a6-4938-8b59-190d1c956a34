package org.dromara.cssrcSoftware.domain.bo;

import org.dromara.cssrcSoftware.domain.SoftwareInfo;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 软件基本信息业务对象 software_info
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SoftwareInfo.class, reverseConvertGenerate = false)
public class SoftwareInfoBo extends BaseEntity {

    /**
     * 软件ID
     */
    @NotNull(message = "软件ID不能为空", groups = { EditGroup.class })
    private Long softwareId;

    /**
     * 软件分类ID
     */
    @NotNull(message = "软件分类ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long categoryId;

    /**
     * 软件名称
     */
    @NotBlank(message = "软件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String softwareName;

    /**
     * 生产厂商
     */
    private String manufacturer;

    /**
     * 生产国别
     */
    private String country;

    /**
     * 软件简介
     */
    private String intro;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 下载次数
     */
    private Long downloadCount;

    /**
     * 软件密级
     */
    @NotNull(message = "软件密级不能为空", groups = { AddGroup.class, EditGroup.class })
    private String secret;


}
