package org.dromara.common.log.content;

import cn.idev.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.service.DictService;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.encrypt.annotation.EncryptField;
import org.dromara.common.encrypt.core.EncryptContext;
import org.dromara.common.encrypt.core.EncryptorManager;
import org.dromara.common.encrypt.enumd.AlgorithmType;
import org.dromara.common.encrypt.enumd.EncodeType;
import org.dromara.common.encrypt.properties.EncryptorProperties;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * 构建操作日志内容元数据
 * <AUTHOR>
 */
@Slf4j
public class OperatorLogMetaDataBuilder {

    /**
     * 封装数据
     * @param object   目标对象
     * @return  封装类
     */
    public OperatorLogMetaData getChangeModel(Object object, Class<?> entityClass) throws IllegalAccessException {
        OperatorLogMetaData result = new OperatorLogMetaData();
        Map<String,String> fieldMap = new HashMap<>(16);

        Class<?> clazz = object.getClass();

        if (entityClass != null) {
            TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
            if (tableInfo != null) {
                String keyProperty = tableInfo.getKeyProperty();
                for (Field field : clazz.getDeclaredFields()) {
                    field.setAccessible(true);
                    Object fieldValue = field.get(object);

                    // 过滤不需要显示的字段
                    if (shouldSkipField(field.getName()) || shouldSkipComplexField(fieldValue)) {
                        continue;
                    }

                    String columnComment = getColumnCommentAndDictValue(entityClass, field.getName(), String.valueOf(fieldValue)).split("=")[0];
                    fieldMap.put(field.getName(), formatFieldValue(fieldValue));

                    if (field.getName().equals(keyProperty)) {
                        result.setId(String.valueOf(fieldValue));
                        result.setName(columnComment);
                    } else if (keyProperty == null && field.getName().toLowerCase().contains("id")) {
                        // 如果没有明确的主键，使用第一个包含id的字段作为标识
                        if (result.getId() == null) {
                            result.setId(String.valueOf(fieldValue));
                            result.setName(columnComment);
                        }
                    }
                }
            }
        }

        result.setFieldMap(fieldMap);

        // 设置对象的显示名称
        String displayName = getObjectDisplayName(object, entityClass);
        if (displayName != null) {
            result.setDisplayName(displayName);
        }

        return result;
    }

    /**
     * 判断是否应该跳过该字段
     */
    private boolean shouldSkipField(String fieldName) {
        // 过滤掉不需要显示的字段
        return "url".equals(fieldName) ||
               "serialVersionUID".equals(fieldName) ||
               "password".equals(fieldName) ||
               "roles".equals(fieldName) ||
               "roleIds".equals(fieldName) ||
               "postIds".equals(fieldName) ||
               "flag".equals(fieldName);
    }

    /**
     * 判断是否应该跳过复杂字段（根据字段值类型）
     */
    private boolean shouldSkipComplexField(Object fieldValue) {
        if (fieldValue == null) {
            return false;
        }

        // 跳过复杂对象（集合、数组、自定义对象等）
        Class<?> valueClass = fieldValue.getClass();

        // 跳过集合类型
        if (java.util.Collection.class.isAssignableFrom(valueClass)) {
            return true;
        }

        // 跳过数组类型（除了基本类型数组）
        if (valueClass.isArray() && !valueClass.getComponentType().isPrimitive()) {
            return true;
        }

        // 跳过自定义对象（非基本类型、非包装类型、非String、非Date）
        if (!isSimpleType(valueClass)) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否为简单类型
     */
    private boolean isSimpleType(Class<?> clazz) {
        return clazz.isPrimitive() ||
               clazz == String.class ||
               clazz == Boolean.class ||
               clazz == Byte.class ||
               clazz == Short.class ||
               clazz == Integer.class ||
               clazz == Long.class ||
               clazz == Float.class ||
               clazz == Double.class ||
               clazz == Character.class ||
               java.util.Date.class.isAssignableFrom(clazz) ||
               java.time.temporal.Temporal.class.isAssignableFrom(clazz);
    }

    /**
     * 格式化字段值
     */
    private String formatFieldValue(Object fieldValue) {
        if (fieldValue == null) {
            return "null";
        }

        // 对于复杂对象，返回简化的字符串表示
        if (!isSimpleType(fieldValue.getClass())) {
            return fieldValue.getClass().getSimpleName();
        }

        return String.valueOf(fieldValue);
    }

    /**
     * 获取数据库字段注释
     */
    public static String getColumnComment(Class<?> clazz, String fieldName) {
        try {
            // 直接根据命名约定获取VO类
            String voClassName = clazz.getName().replace("domain.", "domain.vo.") + "Vo";
            Class<?> voClass = Class.forName(voClassName);
            Field voField = voClass.getDeclaredField(fieldName);
            ExcelProperty excelProperty = voField.getAnnotation(ExcelProperty.class);
            if (excelProperty != null && excelProperty.value().length > 0) {
                return excelProperty.value()[0];
            }
        } catch (Exception e) {
            // 如果获取VO类失败，回退到原字段
            try {
                Field field = clazz.getDeclaredField(fieldName);
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                if (excelProperty != null && excelProperty.value().length > 0) {
                    return excelProperty.value()[0];
                }
            } catch (NoSuchFieldException ex) {
                log.debug("获取字段注解失败", ex);
            }
        }
        return fieldName;
    }

    /**
     * 获取数据库字段注释和枚举值转换
     */
    public static String getColumnCommentAndDictValue(Class<?> clazz, String fieldName, String fieldValue) {
        try {
            // 获取字段注释
            String comment = getColumnComment(clazz, fieldName);

            // 处理字段值
            String processedValue = processFieldValue(clazz, fieldName, fieldValue);

            return comment + "=" + processedValue;
        } catch (Exception e) {
            log.error("处理字段值失败: fieldName={}, error={}", fieldName, e.getMessage());
            return fieldName + "=" + fieldValue;
        }
    }

    /**
     * 处理字段值（解密、字典翻译等）
     */
    private static String processFieldValue(Class<?> entityClass, String fieldName, String fieldValue) {
        if (StringUtils.isBlank(fieldValue) || "null".equals(fieldValue)) {
            return fieldValue;
        }

        try {
            Field field = entityClass.getDeclaredField(fieldName);

            // 处理加密字段
            if (field.isAnnotationPresent(EncryptField.class)) {
//                String decryptedValue = decryptField(fieldValue, field);

                // 如果是secret字段，进行字典翻译
                if ("secret".equals(fieldName)) {
                    return translateDict("sys_file_secret", fieldValue);
                }

                if ("fileSecret".equals(fieldName)) {
                    String decryptedValue = decryptField(fieldValue, field);
                    return translateDict("sys_file_secret", decryptedValue);
                }

                return fieldValue;
            }

            // 处理其他字典字段
            ExcelDictFormat dictFormat = field.getAnnotation(ExcelDictFormat.class);
            if (dictFormat != null) {
                return translateDict(dictFormat.dictType(), fieldValue);
            }

            return fieldValue;
        } catch (Exception e) {
            log.error("处理字段值失败: fieldName={}, error={}", fieldName, e.getMessage());
            return fieldValue;
        }
    }

    /**
     * 解密字段值
     */
    private static String decryptField(String value, Field field) {
        try {
            EncryptorManager encryptorManager = SpringUtils.getBean(EncryptorManager.class);
            EncryptorProperties defaultProperties = SpringUtils.getBean(EncryptorProperties.class);

            EncryptField encryptField = field.getAnnotation(EncryptField.class);
            EncryptContext encryptContext = new EncryptContext();
            encryptContext.setAlgorithm(encryptField.algorithm() == AlgorithmType.DEFAULT ?
                defaultProperties.getAlgorithm() : encryptField.algorithm());
            encryptContext.setEncode(encryptField.encode() == EncodeType.DEFAULT ?
                defaultProperties.getEncode() : encryptField.encode());
            encryptContext.setPassword(StringUtils.isBlank(encryptField.password()) ?
                defaultProperties.getPassword() : encryptField.password());
            encryptContext.setPrivateKey(StringUtils.isBlank(encryptField.privateKey()) ?
                defaultProperties.getPrivateKey() : encryptField.privateKey());
            encryptContext.setPublicKey(StringUtils.isBlank(encryptField.publicKey()) ?
                defaultProperties.getPublicKey() : encryptField.publicKey());

            return encryptorManager.decrypt(value, encryptContext);
        } catch (Exception e) {
            log.error("解密字段失败: {}", e.getMessage());
            return value;
        }
    }

    /**
     * 字典翻译
     */
    private static String translateDict(String dictType, String dictValue) {
        try {
            // 使用DictService进行字典翻译
            DictService dictService = SpringUtils.getBean(DictService.class);
            String dictLabel = dictService.getDictLabel(dictType, dictValue);
            return StringUtils.isNotBlank(dictLabel) ? dictLabel : dictValue;
        } catch (Exception e) {
            log.error("字典翻译失败: dictType={}, dictValue={}, error={}", dictType, dictValue, e.getMessage());
            return dictValue;
        }
    }

    /**
     * 获取对象的显示名称
     * @param object 对象实例
     * @param entityClass 实体类
     * @return 显示名称
     */
    private String getObjectDisplayName(Object object, Class<?> entityClass) {
        try {
            String className = entityClass.getSimpleName();

            // 根据不同的实体类型获取合适的显示名称
            if ("SysUser".equals(className)) {
                return getUserDisplayName(object);
            } else if ("SysRole".equals(className)) {
                return getRoleDisplayName(object);
            } else if ("SysDept".equals(className)) {
                return getDeptDisplayName(object);
            }

            // 默认尝试获取name相关字段
            return getDefaultDisplayName(object);
        } catch (Exception e) {
            log.debug("获取对象显示名称失败", e);
            return null;
        }
    }

    /**
     * 获取用户显示名称
     */
    private String getUserDisplayName(Object user) {
        try {
            Class<?> clazz = user.getClass();

            // 尝试获取nickName
            try {
                Field nickNameField = clazz.getDeclaredField("nickName");
                nickNameField.setAccessible(true);
                String nickName = (String) nickNameField.get(user);

                if (StringUtils.isNotEmpty(nickName)) {
                    // 同时获取userName
                    try {
                        Field userNameField = clazz.getDeclaredField("userName");
                        userNameField.setAccessible(true);
                        String userName = (String) userNameField.get(user);
                        return nickName + "(" + userName + ")";
                    } catch (Exception e) {
                        return nickName;
                    }
                }
            } catch (Exception e) {
                // 忽略
            }

            // 如果没有nickName，尝试获取userName
            try {
                Field userNameField = clazz.getDeclaredField("userName");
                userNameField.setAccessible(true);
                String userName = (String) userNameField.get(user);
                return userName;
            } catch (Exception e) {
                // 忽略
            }
        } catch (Exception e) {
            log.debug("获取用户显示名称失败", e);
        }
        return null;
    }

    /**
     * 获取角色显示名称
     */
    private String getRoleDisplayName(Object role) {
        try {
            Class<?> clazz = role.getClass();
            Field roleNameField = clazz.getDeclaredField("roleName");
            roleNameField.setAccessible(true);
            String roleName = (String) roleNameField.get(role);
            return roleName;
        } catch (Exception e) {
            log.debug("获取角色显示名称失败", e);
        }
        return null;
    }

    /**
     * 获取部门显示名称
     */
    private String getDeptDisplayName(Object dept) {
        try {
            Class<?> clazz = dept.getClass();
            Field deptNameField = clazz.getDeclaredField("deptName");
            deptNameField.setAccessible(true);
            String deptName = (String) deptNameField.get(dept);
            return deptName;
        } catch (Exception e) {
            log.debug("获取部门显示名称失败", e);
        }
        return null;
    }

    /**
     * 获取默认显示名称
     */
    private String getDefaultDisplayName(Object object) {
        try {
            Class<?> clazz = object.getClass();

            // 尝试常见的名称字段
            String[] nameFields = {"name", "title", "label", "text"};
            for (String fieldName : nameFields) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    Object value = field.get(object);
                    if (value != null && StringUtils.isNotEmpty(value.toString())) {
                        return value.toString();
                    }
                } catch (Exception e) {
                    // 继续尝试下一个字段
                }
            }
        } catch (Exception e) {
            log.debug("获取默认显示名称失败", e);
        }
        return null;
    }
}
