package org.dromara.common.log.strategy;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.log.content.ObjectAddLogUtil;
import org.dromara.common.log.content.ObjectDownloadLogUtil;
import org.dromara.common.log.utils.OperatorLogQueryUtil;
import org.dromara.common.log.utils.OperatorLogUtil;
import org.springframework.stereotype.Component;

/**
 * 文件下载生成变更内容模板
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DownloadOperatorLogStrategy implements IOperateLogStrategy {
    @Override
    public String getOperatorContent(Object requestObj, Class<?>[] tableEntity, String operatorObjId) {
        try {
            // 使用ossId获取完整的文件信息
            Object ossInfo = OperatorLogQueryUtil.getObj(String.valueOf(requestObj), tableEntity[0]);
            String objectType = OperatorLogUtil.getTableComment(tableEntity[0]);

            return new ObjectDownloadLogUtil().generatorContent(ossInfo, objectType, tableEntity[0]);

//            String objectType = OperatorLogUtil.getTableComment(tableEntity[0]);
//            ObjectDownloadLogUtil objectDownloadLogUtil = new ObjectDownloadLogUtil();

//            // 使用 ObjectAddLogUtil 生成详细的文件下载日志内容
//            return objectDownloadLogUtil.generatorContent(
//                requestObj,
//                objectType,
//                tableEntity[0]
//            );
        } catch (Exception e) {
            log.error("生成文件下载操作日志失败", e);
            return "生成文件下载日志失败:" + e.getMessage();
        }
    }
}



