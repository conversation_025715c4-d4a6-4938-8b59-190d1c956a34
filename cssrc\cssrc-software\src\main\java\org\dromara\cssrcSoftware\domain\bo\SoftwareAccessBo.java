package org.dromara.cssrcSoftware.domain.bo;

import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.cssrcSoftware.domain.SoftwareAccess;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 软件入网申请业务对象 software_access
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SoftwareAccess.class, reverseConvertGenerate = false)
public class SoftwareAccessBo extends BaseEntity {

    /**
     * 申请id
     */
    private Long accessId;

    /**
     * 入网类型
     */
    @NotBlank(message = "入网类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accessType;

    /**
     * 入网申请
     */
    @NotBlank(message = "入网申请不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accessKind;

    /**
     * 责任人
     */
    @NotNull(message = "责任人不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long accessResponsible;

    /**
     * 计算机联网类型
     */
    @NotBlank(message = "计算机联网类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String networkType;

    /**
     * 计算机密级编号
     */
    @NotBlank(message = "计算机密级编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String computerCode;

    /**
     * 软件分类ID
     */
    private Long categoryId;

    /**
     * 软件ID
     */
    private Long softwareId;

    /**
     * 软件名称
     */
    @NotBlank(message = "软件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String softwareName;

    /**
     * 软件密级
     */
    @NotBlank(message = "软件密级不能为空", groups = { AddGroup.class, EditGroup.class })
    private String secret;

    /**
     * 生产厂商
     */
//    @NotBlank(message = "生产厂商不能为空", groups = { AddGroup.class, EditGroup.class })
    private String manufacturer;

    /**
     * 生产国别
     */
//    @NotBlank(message = "生产国别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String country;

    /**
     * 软件简介
     */
    @NotBlank(message = "软件简介不能为空", groups = { AddGroup.class, EditGroup.class })
    private String intro;

    /**
     * 软件版本ID
     */
    private Long versionId;

    /**
     * 版本号（如 v1.0.0）
     */
    @NotBlank(message = "版本号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String versionName;

    /**
     * 平台（windows/linux）
     */
    @NotBlank(message = "平台（windows/linux）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platform;

    /**
     * 架构（x86/x64/arm64）
     */
    @NotBlank(message = "架构（x86/x64/arm64）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String architecture;

    /**
     * 包类型（exe/msi/deb/rpm/tar.gz）
     */
    @NotBlank(message = "包类型（exe/msi/deb/rpm/tar.gz）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String packageType;

    /**
     * 位数（32/64）
     */
    @NotBlank(message = "位数（32/64）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bits;

    /**
     * 文件id
     */
//    @NotNull(message = "文件id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ossId;

    /**
     * 文件大小(GB)
     */
//    @NotNull(message = "文件大小(GB)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long fileSize;

    /**
     * 版本备注
     */
//    @NotBlank(message = "版本备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;

    /**
     * 流程状态
     */
//    @NotBlank(message = "流程状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String flowStatus;


}
