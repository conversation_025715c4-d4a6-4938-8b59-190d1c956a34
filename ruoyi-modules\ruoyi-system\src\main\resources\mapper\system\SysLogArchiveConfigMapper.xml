<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SysLogArchiveConfigMapper">

    <resultMap type="org.dromara.system.domain.SysLogArchiveConfig" id="SysLogArchiveConfigResult">
        <result property="configId"         column="config_id"         />
        <result property="tenantId"         column="tenant_id"         />
        <result property="retainMonths"     column="retain_months"     />
        <result property="archivePath"      column="archive_path"      />
        <result property="autoArchive"      column="auto_archive"      />
        <result property="createTime"       column="create_time"       />
        <result property="updateTime"       column="update_time"       />
        <result property="lastArchiveTime" column="last_archive_time" />
    </resultMap>

    <sql id="selectSysLogArchiveConfigVo">
        select config_id, tenant_id, retain_months, archive_path,
               auto_archive, create_time, update_time, last_archive_time
        from sys_log_archive_config
    </sql>

</mapper>
