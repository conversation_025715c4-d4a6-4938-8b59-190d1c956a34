package org.dromara.cssrcFinance.controller.budget;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.cssrcFinance.domain.vo.FinanceExpenditurePlanVo;
import org.dromara.cssrcFinance.domain.bo.FinanceExpenditurePlanBo;
import org.dromara.cssrcFinance.service.IFinanceExpenditurePlanService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 财务-预算-支出计划
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/cssrcFinance/budget/expenditurePlan")
public class FinanceExpenditurePlanController extends BaseController {

    private final IFinanceExpenditurePlanService financeExpenditurePlanService;

    /**
     * 查询财务-预算-支出计划列表
     */
    @SaCheckPermission("cssrcFinance:expenditurePlan:list")
    @GetMapping("/list")
    public TableDataInfo<FinanceExpenditurePlanVo> list(FinanceExpenditurePlanBo bo, PageQuery pageQuery) {
        return financeExpenditurePlanService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出财务-预算-支出计划列表
     */
    @SaCheckPermission("cssrcFinance:expenditurePlan:export")
    @Log(title = "财务-预算-支出计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinanceExpenditurePlanBo bo, HttpServletResponse response) {
        List<FinanceExpenditurePlanVo> list = financeExpenditurePlanService.queryList(bo);
        financeExpenditurePlanService.handleExportExcelData(list);
        ExcelUtil.exportExcel(list, "财务-预算-支出计划", FinanceExpenditurePlanVo.class, response);
    }

    /**
     * 获取财务-预算-支出计划详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("cssrcFinance:expenditurePlan:query")
    @GetMapping("/{id}")
    public R<FinanceExpenditurePlanVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(financeExpenditurePlanService.queryById(id));
    }

    /**
     * 新增财务-预算-支出计划
     */
    @SaCheckPermission("cssrcFinance:expenditurePlan:add")
    @Log(title = "财务-预算-支出计划", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<FinanceExpenditurePlanVo> add(@Validated(AddGroup.class) @RequestBody FinanceExpenditurePlanBo bo) {
        return R.ok(financeExpenditurePlanService.insertByBo(bo));
    }

    /**
     * 修改财务-预算-支出计划
     */
    @SaCheckPermission("cssrcFinance:expenditurePlan:edit")
    @Log(title = "财务-预算-支出计划", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<FinanceExpenditurePlanVo> edit(@Validated(EditGroup.class) @RequestBody FinanceExpenditurePlanBo bo) {
        return R.ok(financeExpenditurePlanService.updateByBo(bo));
    }

    /**
     * 删除财务-预算-支出计划
     *
     * @param ids 主键串
     */
    @SaCheckPermission("cssrcFinance:expenditurePlan:remove")
    @Log(title = "财务-预算-支出计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(financeExpenditurePlanService.deleteWithValidByIds(List.of(ids), true));
    }
}
